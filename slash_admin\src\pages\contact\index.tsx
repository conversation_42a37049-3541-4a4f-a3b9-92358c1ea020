import React, { useEffect, useState } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Card,
  Row,
  Col,
  Tooltip,
  Popconfirm,
  message,
  Tag,
  Avatar,
  Drawer
} from 'antd';
import type { ColumnsType, TableProps } from 'antd/es/table';
import type { FilterValue, SorterResult } from 'antd/es/table/interface';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  PhoneOutlined,
  MessageOutlined,
  UserOutlined
} from '@ant-design/icons';
import { toast } from 'sonner';
import dayjs from 'dayjs';

import messageService from '../../api/services/messageService';
import { Contact } from '@/types';

const { Search } = Input;
const { Option } = Select;


interface TableParams {
  pagination?: {
    current?: number;
    pageSize?: number;
    total?: number;
  };
  sortField?: string;
  sortOrder?: string;
  filters?: Record<string, FilterValue | null>;
}

export default function ContactPage() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [showDetail, setShowDetail] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingContact, setEditingContact] = useState<Contact | null>(null);
  const [searchText, setSearchText] = useState('');
  const [filteredInfo, setFilteredInfo] = useState<Record<string, FilterValue | null>>({});
  const [sortedInfo, setSortedInfo] = useState<SorterResult<Contact>>({});

  const [tableParams, setTableParams] = useState<TableParams>({
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0,
    },
  });

  const [form] = Form.useForm();

  // Fetch contacts data
  const fetchContacts = async (params?: any) => {
    setLoading(true);
    try {
      const filterParams: any = {
        page: params?.pagination?.current || 1,
        page_size: params?.pagination?.pageSize || 20,
        search: searchText,
        ordering: params?.sortField ? `${params?.sortOrder === 'ascend' ? '' : '-'}${params?.sortField}` : '-created_at'
      };

      // Add table filters
      if (params?.filters) {
        Object.keys(params.filters).forEach(key => {
          if (params.filters[key]) {
            filterParams[key] = params.filters[key];
          }
        });
      }

      const response = await messageService.getContacts(filterParams);

      setContacts(response.results || []);
      setTableParams({
        pagination: {
          current: params?.pagination?.current || 1,
          pageSize: params?.pagination?.pageSize || 20,
          total: response.count || 0,
        },
      });
    } catch (error) {
      console.error('Error fetching contacts:', error);
      toast.error('Failed to fetch contacts');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchContacts(tableParams);
  }, [searchText]);

  // Action handlers
  const handleTableChange: TableProps<Contact>['onChange'] = (pagination, filters, sorter) => {
    const params = {
      pagination,
      filters,
      sortField: Array.isArray(sorter) ? undefined : sorter.field as string,
      sortOrder: Array.isArray(sorter) ? undefined : sorter.order as string,
    };

    setTableParams(params);
    setFilteredInfo(filters || {});
    setSortedInfo(Array.isArray(sorter) ? {} : sorter);
    fetchContacts(params);
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  const handleRefresh = () => {
    fetchContacts(tableParams);
  };

  const handleCreate = () => {
    setEditingContact(null);
    form.resetFields();
    setShowCreateModal(true);
  };

  const handleEdit = (record: Contact) => {
    setEditingContact(record);
    form.setFieldsValue({
      ...record,
      created_at: record.created_at ? dayjs(record.created_at) : undefined,
    });
    setShowCreateModal(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await messageService.deleteContact(id);
      message.success('Contact deleted successfully');
      fetchContacts(tableParams);
    } catch (error) {
      console.error('Error deleting contact:', error);
      message.error('Failed to delete contact');
    }
  };

  const handleView = (record: Contact) => {
    setSelectedContact(record);
    setShowDetail(true);
  };

  const handleFormSubmit = async (values: any) => {
    try {
      if (editingContact) {
        await messageService.updateContact(editingContact.id, values);
        message.success('Contact updated successfully');
      } else {
        await messageService.createContact(values);
        message.success('Contact created successfully');
      }
      setShowCreateModal(false);
      fetchContacts(tableParams);
    } catch (error) {
      console.error('Error saving contact:', error);
      message.error('Failed to save contact');
    }
  };

  // Table columns
  const columns: ColumnsType<Contact> = [
    {
      title: 'Contact',
      key: 'contact',
      width: 250,
      render: (_, record: Contact) => (
        <div className="flex items-center space-x-3">
          <Avatar
            size={40}
            icon={<UserOutlined />}
            style={{ backgroundColor: '#1890ff' }}
          >
            {record.name?.charAt(0)?.toUpperCase()}
          </Avatar>
          <div>
            <div className="font-medium text-gray-900">{record.name || 'Unknown'}</div>
            <div className="text-sm text-gray-500">{record.phone_number}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      width: 150,
      render: (location: string) => location || 'N/A',
    },
    {
      title: 'Language',
      dataIndex: 'preferred_language',
      key: 'preferred_language',
      width: 120,
      render: (language: string) => (
        <Tag color="blue">{language || 'N/A'}</Tag>
      ),
    },
    {
      title: 'Opt-in Status',
      dataIndex: 'opt_in_status',
      key: 'opt_in_status',
      width: 120,
      filters: [
        { text: 'Opted In', value: true },
        { text: 'Opted Out', value: false },
      ],
      filteredValue: filteredInfo.opt_in_status || null,
      render: (optIn: boolean) => (
        <Tag color={optIn ? 'green' : 'red'}>
          {optIn ? 'Opted In' : 'Opted Out'}
        </Tag>
      ),
    },
    {
      title: 'Messages',
      dataIndex: 'message_count',
      key: 'message_count',
      width: 100,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'message_count' ? sortedInfo.order : null,
      render: (count: number) => (
        <div className="flex items-center space-x-1">
          <MessageOutlined className="text-gray-400" />
          <span>{count || 0}</span>
        </div>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'created_at' ? sortedInfo.order : null,
      render: (date: string) => (
        <span className="text-sm">
          {date ? dayjs(date).format('MMM DD, YYYY') : '-'}
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record: Contact) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this contact?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];



  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <Card className="mb-4">
        <Row justify="space-between" align="middle">
          <Col>
            <div className="flex items-center space-x-3">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Contact Management</h1>
                <p className="text-sm text-gray-500">Manage and organize your contacts</p>
              </div>
            </div>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="Search contacts..."
                allowClear
                onSearch={handleSearch}
                style={{ width: 250 }}
                enterButton={<SearchOutlined />}
              />
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreate}
              >
                New Contact
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              >
                Refresh
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Table */}
      <Card className="flex-1">
        <Table<Contact>
          columns={columns}
          dataSource={contacts}
          rowKey="id"
          loading={loading}
          pagination={{
            ...tableParams.pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} contacts`,
          }}
          onChange={handleTableChange}
          scroll={{ y: 'calc(100vh - 350px)' }}
          size="small"
        />
      </Card>

      {/* Create/Edit Modal */}
      <Modal
        title={editingContact ? 'Edit Contact' : 'Create New Contact'}
        open={showCreateModal}
        onCancel={() => setShowCreateModal(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
          initialValues={{
            opt_in_status: true,
            preferred_language: 'en',
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Name"
                rules={[{ required: true, message: 'Please enter contact name' }]}
              >
                <Input placeholder="Enter contact name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone_number"
                label="Phone Number"
                rules={[
                  { required: true, message: 'Please enter phone number' },
                  { pattern: /^(?:\+251\d{9}|0\d{9})$/, message: 'Please enter a valid phone number' }
                  // { pattern: /^\+?[1-9]\d{1,14}$/, message: 'Please enter a valid phone number' }

                ]}
              >
                <Input placeholder="Enter phone number" prefix={<PhoneOutlined />} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="location" label="Location">
                <Input placeholder="Enter location" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="preferred_language" label="Preferred Language">
                <Select>
                  <Option value="en">English</Option>
                  <Option value="am">Amharic</Option>
                  <Option value="or">Oromo</Option>
                  <Option value="ti">Tigrinya</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="opt_in_status" label="Opt-in Status" valuePropName="checked">
            <Switch checkedChildren="Opted In" unCheckedChildren="Opted Out" />
          </Form.Item>

          <Form.Item className="mb-0">
            <Space className="w-full justify-end">
              <Button onClick={() => setShowCreateModal(false)}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {editingContact ? 'Update Contact' : 'Create Contact'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Contact Detail Drawer */}
      <Drawer
        title="Contact Details"
        placement="right"
        size="large"
        open={showDetail}
        onClose={() => setShowDetail(false)}
        width={600}
      >
        {selectedContact && (
          <div className="space-y-6">
            <div className="text-center">
              <Avatar
                size={80}
                icon={<UserOutlined />}
                style={{ backgroundColor: '#1890ff' }}
              >
                {selectedContact.name?.charAt(0)?.toUpperCase()}
              </Avatar>
              <h2 className="mt-4 text-xl font-semibold">{selectedContact.name || 'Unknown'}</h2>
              <p className="text-gray-500">{selectedContact.phone_number}</p>
            </div>

            <Card title="Contact Information" size="small">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Location</p>
                    <p className="text-sm text-gray-900">{selectedContact.location || 'N/A'}</p>
                  </div>
                </Col>
                <Col span={12}>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Language</p>
                    <Tag color="blue">{selectedContact.preferred_language || 'N/A'}</Tag>
                  </div>
                </Col>
                <Col span={12}>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Opt-in Status</p>
                    <Tag color={selectedContact.opt_in_status ? 'green' : 'red'}>
                      {selectedContact.opt_in_status ? 'Opted In' : 'Opted Out'}
                    </Tag>
                  </div>
                </Col>
                <Col span={12}>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Message Count</p>
                    <p className="text-sm text-gray-900">{selectedContact.message_count || 0}</p>
                  </div>
                </Col>
                <Col span={12}>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Created</p>
                    <p className="text-sm text-gray-900">
                      {selectedContact.created_at ? dayjs(selectedContact.created_at).format('MMM DD, YYYY HH:mm') : 'N/A'}
                    </p>
                  </div>
                </Col>
                <Col span={12}>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Last Updated</p>
                    <p className="text-sm text-gray-900">
                      {selectedContact.updated_at ? dayjs(selectedContact.updated_at).format('MMM DD, YYYY HH:mm') : 'N/A'}
                    </p>
                  </div>
                </Col>
              </Row>
            </Card>

            <div className="flex space-x-2">
              <Button
                type="primary"
                icon={<EditOutlined />}
                onClick={() => {
                  setShowDetail(false);
                  handleEdit(selectedContact);
                }}
              >
                Edit Contact
              </Button>
              <Button
                icon={<MessageOutlined />}
                onClick={() => {
                  // Navigate to messages with this contact
                  window.location.href = `/messages?contact=${selectedContact.id}`;
                }}
              >
                View Messages
              </Button>
            </div>
          </div>
        )}
      </Drawer>
    </div>
  );
}