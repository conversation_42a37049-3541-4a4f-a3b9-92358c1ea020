import { 
  Tenant, User, Role, Case, CaseEvent, Task, Document, Party, CaseParty, 
  SLA, ReportConfig, Notification, DashboardMetrics 
} from './types';

// Mock Tenants
export const mockTenants: Tenant[] = [
  {
    id: 1,
    name: 'City of Springfield',
    slug: 'springfield',
    active: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    name: 'Metro Utilities Corp',
    slug: 'metro-utilities',
    active: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

// Mock Roles
export const mockRoles: Role[] = [
  {
    id: 1,
    tenantId: 1,
    name: 'Admin',
    permissions: ['*'],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    tenantId: 1,
    name: 'Compliance Officer',
    permissions: ['cases:read', 'cases:write', 'cases:assign', 'reports:read', 'reports:write'],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 3,
    tenantId: 1,
    name: 'Investigator',
    permissions: ['cases:read', 'cases:write', 'tasks:read', 'tasks:write', 'documents:read', 'documents:write'],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 4,
    tenantId: 1,
    name: 'Viewer',
    permissions: ['cases:read', 'reports:read'],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

// Mock Users
export const mockUsers: User[] = [
  {
    id: 1,
    tenantId: 1,
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    roleId: 1,
    status: 'active',
    lastLoginAt: '2024-01-20T09:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T09:30:00Z'
  },
  {
    id: 2,
    tenantId: 1,
    name: 'Michael Chen',
    email: '<EMAIL>',
    roleId: 2,
    status: 'active',
    lastLoginAt: '2024-01-20T08:15:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T08:15:00Z'
  },
  {
    id: 3,
    tenantId: 1,
    name: 'Emily Rodriguez',
    email: '<EMAIL>',
    roleId: 3,
    status: 'active',
    lastLoginAt: '2024-01-20T10:45:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T10:45:00Z'
  },
  {
    id: 4,
    tenantId: 1,
    name: 'David Wilson',
    email: '<EMAIL>',
    roleId: 3,
    status: 'active',
    lastLoginAt: '2024-01-19T16:20:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-19T16:20:00Z'
  },
  {
    id: 5,
    tenantId: 1,
    name: 'Lisa Thompson',
    email: '<EMAIL>',
    roleId: 4,
    status: 'active',
    lastLoginAt: '2024-01-20T07:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T07:30:00Z'
  }
];

// Mock Parties
export const mockParties: Party[] = [
  {
    id: 1,
    tenantId: 1,
    type: 'Person',
    name: 'John Doe',
    contact: {
      email: '<EMAIL>',
      phone: '******-0123',
      address: '123 Main St, Springfield, IL 62701'
    },
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-10T00:00:00Z'
  },
  {
    id: 2,
    tenantId: 1,
    type: 'Organization',
    name: 'Springfield Environmental Group',
    contact: {
      email: '<EMAIL>',
      phone: '******-0456',
      address: '456 Oak Ave, Springfield, IL 62701'
    },
    createdAt: '2024-01-12T00:00:00Z',
    updatedAt: '2024-01-12T00:00:00Z'
  },
  {
    id: 3,
    tenantId: 1,
    type: 'Person',
    name: 'Jane Smith',
    contact: {
      email: '<EMAIL>',
      phone: '******-0789'
    },
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z'
  }
];

// Mock Cases
export const mockCases: Case[] = [
  {
    id: 1,
    tenantId: 1,
    caseNo: 'COMP-2024-001',
    title: 'Improper Waste Disposal at Construction Site',
    description: 'Anonymous report of construction company dumping materials in protected wetland area. Requires immediate investigation and potential enforcement action.',
    category: 'Compliance',
    priority: 'High',
    status: 'Investigating',
    reporterType: 'Anonymous',
    reporterContact: {
      anonymous: true,
      submissionId: 'anon-20240110-001'
    },
    createdBy: 2,
    assignedTo: 3,
    slaFirstResponseDue: '2024-01-11T16:00:00Z',
    slaResolutionDue: '2024-01-25T16:00:00Z',
    isOverdue: false,
    isRestricted: false,
    createdAt: '2024-01-10T14:30:00Z',
    updatedAt: '2024-01-18T11:20:00Z'
  },
  {
    id: 2,
    tenantId: 1,
    caseNo: 'COMP-2024-002',
    title: 'Noise Ordinance Violation - Industrial Facility',
    description: 'Multiple citizen complaints about excessive noise from manufacturing facility operating outside permitted hours.',
    category: 'Complaint',
    priority: 'Medium',
    status: 'AwaitingAction',
    reporterType: 'Citizen',
    reporterContact: {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '******-0123'
    },
    createdBy: 2,
    assignedTo: 4,
    slaFirstResponseDue: '2024-01-13T16:00:00Z',
    slaResolutionDue: '2024-02-12T16:00:00Z',
    isOverdue: true,
    isRestricted: false,
    createdAt: '2024-01-12T09:15:00Z',
    updatedAt: '2024-01-19T14:45:00Z'
  },
  {
    id: 3,
    tenantId: 1,
    caseNo: 'AUD-2024-001',
    title: 'Annual Environmental Compliance Audit - Water Treatment',
    description: 'Scheduled annual audit of water treatment facility compliance with EPA regulations and local ordinances.',
    category: 'Audit',
    priority: 'Medium',
    status: 'UnderReview',
    reporterType: 'Regulator',
    reporterContact: {
      name: 'EPA Region 5',
      email: '<EMAIL>',
      organization: 'Environmental Protection Agency'
    },
    createdBy: 1,
    assignedTo: 2,
    slaFirstResponseDue: '2024-01-16T16:00:00Z',
    slaResolutionDue: '2024-03-15T16:00:00Z',
    isOverdue: false,
    isRestricted: true,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T08:30:00Z'
  },
  {
    id: 4,
    tenantId: 1,
    caseNo: 'INC-2024-001',
    title: 'Chemical Spill at Industrial Park',
    description: 'Emergency response to chemical spill incident. Immediate containment and investigation required.',
    category: 'Incident',
    priority: 'Critical',
    status: 'Resolved',
    reporterType: 'Employee',
    reporterContact: {
      name: 'Emergency Response Team',
      email: '<EMAIL>',
      department: 'Fire Department'
    },
    createdBy: 1,
    assignedTo: 3,
    slaFirstResponseDue: '2024-01-08T15:00:00Z',
    slaResolutionDue: '2024-01-15T15:00:00Z',
    isOverdue: false,
    isRestricted: true,
    createdAt: '2024-01-08T14:30:00Z',
    updatedAt: '2024-01-14T16:45:00Z',
    resolvedAt: '2024-01-14T16:45:00Z'
  },
  {
    id: 5,
    tenantId: 1,
    caseNo: 'COMP-2024-003',
    title: 'Unauthorized Tree Removal in Protected Area',
    description: 'Report of tree removal in designated conservation area without proper permits.',
    category: 'Compliance',
    priority: 'High',
    status: 'New',
    reporterType: 'Citizen',
    reporterContact: {
      name: 'Springfield Environmental Group',
      email: '<EMAIL>',
      phone: '******-0456'
    },
    createdBy: 2,
    slaFirstResponseDue: '2024-01-21T16:00:00Z',
    slaResolutionDue: '2024-02-04T16:00:00Z',
    isOverdue: false,
    isRestricted: false,
    createdAt: '2024-01-20T11:30:00Z',
    updatedAt: '2024-01-20T11:30:00Z'
  }
];

// Mock Case Events
export const mockCaseEvents: CaseEvent[] = [
  {
    id: 1,
    caseId: 1,
    type: 'StatusChange',
    data: {
      oldValue: 'New',
      newValue: 'UnderReview',
      note: 'Initial review completed, assigning to investigator'
    },
    createdBy: 2,
    createdAt: '2024-01-10T15:00:00Z'
  },
  {
    id: 2,
    caseId: 1,
    type: 'Assignment',
    data: {
      oldValue: null,
      newValue: 3,
      note: 'Assigned to Emily Rodriguez for field investigation'
    },
    createdBy: 2,
    createdAt: '2024-01-10T15:05:00Z'
  },
  {
    id: 3,
    caseId: 1,
    type: 'StatusChange',
    data: {
      oldValue: 'UnderReview',
      newValue: 'Investigating',
      note: 'Field investigation commenced'
    },
    createdBy: 3,
    createdAt: '2024-01-11T09:30:00Z'
  },
  {
    id: 4,
    caseId: 1,
    type: 'Note',
    data: {
      note: 'Site visit completed. Evidence of improper disposal confirmed. Photos and samples collected.'
    },
    createdBy: 3,
    createdAt: '2024-01-12T14:20:00Z'
  },
  {
    id: 5,
    caseId: 2,
    type: 'StatusChange',
    data: {
      oldValue: 'Investigating',
      newValue: 'AwaitingAction',
      note: 'Investigation complete. Awaiting corrective action from facility owner.'
    },
    createdBy: 4,
    createdAt: '2024-01-19T14:45:00Z'
  }
];

// Mock Tasks
export const mockTasks: Task[] = [
  {
    id: 1,
    caseId: 1,
    title: 'Site Inspection and Evidence Collection',
    description: 'Conduct thorough site inspection of reported dumping area. Collect photographic evidence and soil samples.',
    type: 'Investigation',
    assigneeId: 3,
    dueAt: '2024-01-15T16:00:00Z',
    status: 'Done',
    checklist: [
      { id: 1, text: 'Photograph dumping area', completed: true, completedAt: '2024-01-12T10:30:00Z', completedBy: 3 },
      { id: 2, text: 'Collect soil samples', completed: true, completedAt: '2024-01-12T11:15:00Z', completedBy: 3 },
      { id: 3, text: 'Interview site supervisor', completed: true, completedAt: '2024-01-12T14:00:00Z', completedBy: 3 },
      { id: 4, text: 'Document GPS coordinates', completed: true, completedAt: '2024-01-12T10:45:00Z', completedBy: 3 }
    ],
    createdBy: 2,
    createdAt: '2024-01-10T15:10:00Z',
    updatedAt: '2024-01-12T14:20:00Z',
    completedAt: '2024-01-12T14:20:00Z'
  },
  {
    id: 2,
    caseId: 1,
    title: 'Laboratory Analysis of Samples',
    description: 'Submit collected samples to certified lab for contamination analysis.',
    type: 'EvidenceReview',
    assigneeId: 3,
    dueAt: '2024-01-22T16:00:00Z',
    status: 'InProgress',
    checklist: [
      { id: 5, text: 'Submit samples to lab', completed: true, completedAt: '2024-01-13T09:00:00Z', completedBy: 3 },
      { id: 6, text: 'Follow up on analysis timeline', completed: false },
      { id: 7, text: 'Review lab results', completed: false }
    ],
    createdBy: 3,
    createdAt: '2024-01-12T14:25:00Z',
    updatedAt: '2024-01-18T11:20:00Z'
  },
  {
    id: 3,
    caseId: 2,
    title: 'Noise Level Monitoring',
    description: 'Conduct 48-hour noise monitoring at facility perimeter during reported violation hours.',
    type: 'Investigation',
    assigneeId: 4,
    dueAt: '2024-01-20T16:00:00Z',
    status: 'Done',
    createdBy: 2,
    createdAt: '2024-01-12T10:00:00Z',
    updatedAt: '2024-01-18T15:30:00Z',
    completedAt: '2024-01-18T15:30:00Z'
  },
  {
    id: 4,
    caseId: 2,
    title: 'Issue Violation Notice',
    description: 'Prepare and issue formal violation notice to facility management.',
    type: 'CorrectiveAction',
    assigneeId: 2,
    dueAt: '2024-01-25T16:00:00Z',
    status: 'Open',
    createdBy: 4,
    createdAt: '2024-01-19T14:50:00Z',
    updatedAt: '2024-01-19T14:50:00Z'
  }
];

// Mock Documents
export const mockDocuments: Document[] = [
  {
    id: 1,
    caseId: 1,
    fileKey: 'cases/1/site-photos-20240112.zip',
    filename: 'site-photos-20240112.zip',
    mimeType: 'application/zip',
    size: 15728640,
    uploadedBy: 3,
    hash: 'sha256:a1b2c3d4e5f6...',
    tags: ['evidence', 'photos', 'site-inspection'],
    isEvidence: true,
    description: 'Photographic evidence from site inspection showing improper waste disposal',
    createdAt: '2024-01-12T14:30:00Z'
  },
  {
    id: 2,
    caseId: 1,
    fileKey: 'cases/1/soil-samples-chain-of-custody.pdf',
    filename: 'soil-samples-chain-of-custody.pdf',
    mimeType: 'application/pdf',
    size: 524288,
    uploadedBy: 3,
    hash: 'sha256:b2c3d4e5f6g7...',
    tags: ['evidence', 'chain-of-custody', 'samples'],
    isEvidence: true,
    description: 'Chain of custody documentation for soil samples',
    createdAt: '2024-01-13T09:15:00Z'
  },
  {
    id: 3,
    caseId: 2,
    fileKey: 'cases/2/noise-monitoring-report.pdf',
    filename: 'noise-monitoring-report.pdf',
    mimeType: 'application/pdf',
    size: 1048576,
    uploadedBy: 4,
    hash: 'sha256:c3d4e5f6g7h8...',
    tags: ['evidence', 'noise-monitoring', 'report'],
    isEvidence: true,
    description: '48-hour noise monitoring results showing violations',
    createdAt: '2024-01-18T16:00:00Z'
  },
  {
    id: 4,
    caseId: 3,
    fileKey: 'cases/3/audit-checklist-2024.xlsx',
    filename: 'audit-checklist-2024.xlsx',
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    size: 262144,
    uploadedBy: 2,
    hash: 'sha256:d4e5f6g7h8i9...',
    tags: ['audit', 'checklist', 'compliance'],
    isEvidence: false,
    description: 'Annual compliance audit checklist',
    createdAt: '2024-01-15T11:00:00Z'
  }
];

// Mock SLAs
export const mockSLAs: SLA[] = [
  {
    id: 1,
    tenantId: 1,
    name: 'Critical Incident Response',
    appliesTo: {
      categories: ['Incident'],
      priorities: ['Critical']
    },
    firstResponseHours: 1,
    resolutionHours: 24,
    escalationRules: [
      {
        hours: 2,
        action: 'escalate_to_supervisor',
        recipients: [1, 2]
      }
    ],
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    tenantId: 1,
    name: 'High Priority Compliance',
    appliesTo: {
      categories: ['Compliance'],
      priorities: ['High']
    },
    firstResponseHours: 4,
    resolutionHours: 240,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 3,
    tenantId: 1,
    name: 'Standard Complaint',
    appliesTo: {
      categories: ['Complaint'],
      priorities: ['Medium', 'Low']
    },
    firstResponseHours: 24,
    resolutionHours: 720,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
];

// Mock Dashboard Metrics
export const mockDashboardMetrics: DashboardMetrics = {
  totalCases: 47,
  openCases: 23,
  overdueCases: 3,
  resolvedThisMonth: 18,
  averageResolutionTime: 156, // hours
  slaBreaches: 5,
  casesByStatus: {
    'New': 8,
    'UnderReview': 5,
    'Investigating': 7,
    'AwaitingAction': 3,
    'Resolved': 18,
    'Closed': 5,
    'Archived': 1
  },
  casesByCategory: {
    'Compliance': 22,
    'Complaint': 15,
    'Audit': 6,
    'Incident': 4
  },
  casesByPriority: {
    'Critical': 2,
    'High': 12,
    'Medium': 25,
    'Low': 8
  },
  monthlyTrends: [
    { month: '2023-08', created: 12, resolved: 10, breaches: 1 },
    { month: '2023-09', created: 15, resolved: 14, breaches: 2 },
    { month: '2023-10', created: 18, resolved: 16, breaches: 1 },
    { month: '2023-11', created: 14, resolved: 17, breaches: 0 },
    { month: '2023-12', created: 11, resolved: 13, breaches: 1 },
    { month: '2024-01', created: 19, resolved: 18, breaches: 3 }
  ],
  topAssignees: [
    { userId: 3, name: 'Emily Rodriguez', caseCount: 12, avgResolutionTime: 142 },
    { userId: 4, name: 'David Wilson', caseCount: 10, avgResolutionTime: 168 },
    { userId: 2, name: 'Michael Chen', caseCount: 8, avgResolutionTime: 134 }
  ]
};

// Helper functions to enrich data with relationships
export const enrichCasesWithRelations = (cases: Case[]): Case[] => {
  return cases.map(caseItem => ({
    ...caseItem,
    createdByUser: mockUsers.find(u => u.id === caseItem.createdBy),
    assignedToUser: mockUsers.find(u => u.id === caseItem.assignedTo),
    events: mockCaseEvents.filter(e => e.caseId === caseItem.id).map(event => ({
      ...event,
      createdByUser: mockUsers.find(u => u.id === event.createdBy)
    })),
    tasks: mockTasks.filter(t => t.caseId === caseItem.id).map(task => ({
      ...task,
      assignee: mockUsers.find(u => u.id === task.assigneeId)
    })),
    documents: mockDocuments.filter(d => d.caseId === caseItem.id).map(doc => ({
      ...doc,
      uploadedByUser: mockUsers.find(u => u.id === doc.uploadedBy)
    }))
  }));
};

export const enrichUsersWithRoles = (users: User[]): User[] => {
  return users.map(user => ({
    ...user,
    role: mockRoles.find(r => r.id === user.roleId)
  }));
};

// Export enriched data
export const enrichedCases = enrichCasesWithRelations(mockCases);
export const enrichedUsers = enrichUsersWithRoles(mockUsers);