import React from 'react';
import { Conversation } from '../../../../types';
import {
  UserIcon,
  ClockIcon,
  PhoneIcon,
} from '@heroicons/react/24/outline';

interface ConversationListItemProps {
  conversation: Conversation;
  isSelected: boolean;
  onSelect: () => void;
}

const ConversationListItem: React.FC<ConversationListItemProps> = ({
  conversation,
  isSelected,
  onSelect,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'resolved': return 'text-blue-600 bg-blue-100';
      case 'closed': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      return `${Math.floor(diffInHours * 60)}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  console.log("conversation     mmmmmmmmmmmmmmmmmmmmm", conversation);

  return (
    <div
      onClick={onSelect}
      className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'bg-blue-50 border-blue-300 shadow-md'
          : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300'
      }`}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          <div className="flex-shrink-0">
            <div className={`h-10 w-10 rounded-full flex items-center justify-center ${isSelected ? 'bg-blue-100' : 'bg-gray-100'}`}>
              <UserIcon className={`h-6 w-6 ${isSelected ? 'text-blue-600' : 'text-gray-500'}`} />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <span className="text-sm font-semibold text-gray-900 truncate">
                {conversation.contact_name || 'Unknown Contact'}
              </span>
              {conversation.unread_count > 0 && (
                <span className="ml-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                  {conversation.unread_count}
                </span>
              )}
            </div>
            <p className="text-xs text-gray-500 truncate">
              {conversation.contact_phone || 'Unknown Numberkkkkkkkkkkkkk'}
            </p>
            {(conversation as any).last_message_preview?.content && (
              <p className="text-sm text-gray-600 mt-1 truncate">
                {(conversation as any).last_message_preview.content}
              </p>
            )}
          </div>
        </div>
        
        <div className="flex flex-col items-end space-y-2 text-xs">
          <span className={`inline-flex px-2 py-1 font-semibold rounded-full ${getStatusColor(conversation.status)}`}>
            {conversation.status}
          </span>
          {conversation.last_message_at && (
            <div className="flex items-center space-x-1 text-gray-500">
              <ClockIcon className="h-3 w-3" />
              <span>{formatDate(conversation.last_message_at)}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConversationListItem;
