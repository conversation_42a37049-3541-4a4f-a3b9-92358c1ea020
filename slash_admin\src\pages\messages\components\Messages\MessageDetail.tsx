import React, { useEffect, useRef, useState, useCallback } from 'react';
import { format, isToday } from 'date-fns';
import { toast } from 'sonner';
import {
  UserIcon,
  ClockIcon,
  TagIcon,
  PaperAirplaneIcon,
  DocumentArrowDownIcon,
  UserPlusIcon,
  ClipboardDocumentListIcon,
  ChatBubbleLeftRightIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import { SMSMessage, User, TimelineEvent, ProgressReport, Reply } from '../../../../types';
import { useAuth } from '../../../../contexts/AuthContext';
import { useMessages } from '../../../../hooks/useMessages';
import messageService from '../../../../api/services/messageService';
import StatusBadge from './StatusBadge';
import CategoryBadge from './CategoryBadge';
import PriorityBadge from './PriorityBadge';
import ReplyBox from './ReplyBox';
import AssignmentModal from './AssignmentModal';
import EnhancedAssignmentModal from './EnhancedAssignmentModal';
import ApprovalModal from './ApprovalModal';
import DetailedReportModal from '../Reports/DetailedReportModal';
import ProgressReportModal from './ProgressReportModal';
import CaseTimeline from './CaseTimeline';
import AttachmentViewer from './AttachmentViewer';
import MessageActions from './MessageActions';
import { mockUsers } from '@/data/mockData';

interface MessageDetailProps {
  message: SMSMessage;
}

export default function MessageDetail({ message }: MessageDetailProps) {
  const { currentUser } = useAuth();
  const {
    assignMessage,
    updateMessageStatus,
    addTag,
    archiveMessage,
    restoreMessage,
    deleteMessage,
    exportToPDF,
    selectedContactMessages
  } = useMessages();

  // Reply filtering states
  const [replyFilters, setReplyFilters] = useState({
    phoneNumber: '',
    search: '',
    senderType: '' as '' | 'customer' | 'agent'
  });
  const [showReplyFilters, setShowReplyFilters] = useState(false);

  

  // Simple replies state - no pagination
  const [replies, setReplies] = useState<Reply[]>([]);
  const [repliesLoading, setRepliesLoading] = useState(false);
  const [repliesError, setRepliesError] = useState<string | null>(null);

  // Fetch all replies for the message
  const fetchReplies = useCallback(async () => {
    try {
      setRepliesLoading(true);
      setRepliesError(null);

      const params = {
        message_id: message.id,
        page_size: 1000, // Get all replies at once
        ...(replyFilters.phoneNumber && { phone_number: replyFilters.phoneNumber }),
        ...(replyFilters.search && { search: replyFilters.search }),
        ...(replyFilters.senderType && { sender_type: replyFilters.senderType })
      };

      const response = await messageService.getReplies(params);
      // Normalize replies
      const normalizedReplies: Reply[] = (response.results || response).map((reply: any) => ({
        id: reply.id,
        content: reply.content,
        timestamp: new Date(reply.timestamp),
        sender: reply.sender ? {
          id: reply.sender.id,
          name: reply.sender.name || reply.sender.username,
          email: reply.sender.email,
          avatar: reply.sender.avatar,
          role: reply.sender.role || 'technician',
          department: reply.sender.department || 'Operations',
          isActive: reply.sender.is_active !== false
        } : null,
        is_from_customer: reply.direction === 'inbound'? true : false,
        direction: reply.direction || 'inbound',
        status: reply.status,
        attachments: reply.attachments || []
      }));

      setReplies(normalizedReplies);
    } catch (err: any) {
      console.error('Error fetching replies:', err);
      setRepliesError(err.response?.data?.error || 'Failed to fetch replies');
    } finally {
      setRepliesLoading(false);
    }
  }, [message.id, replyFilters.phoneNumber, replyFilters.search, replyFilters.senderType]);

  // Add a new reply
  const addReplyToMessage = useCallback(async (content: string) => {
    try {
      const response = await messageService.addReply(message.id, content);
      console.log('Reply response:', response);

      // The backend now returns the created message data
      const normalizedReply: Reply = {
        id: response.id || `reply-${Date.now()}`,
        content: response.content || content,
        timestamp: new Date(response.created_at || Date.now()),
        sender: {
          id: 'agent',
          name: 'Agent',
          email: '<EMAIL>',
          role: 'technician' as const,
          department: 'Support',
          isActive: true
        },
        is_from_customer: false, // Replies from frontend are always from agent
        direction: 'outbound' as const, // Replies are always outbound
        status: response.status || 'sent',
        attachments: []
      };

      setReplies(prev => [...prev, normalizedReply]);

      // Show success message
      toast.success('Reply sent successfully');

      return normalizedReply;
    } catch (err: any) {
      console.error('Error adding reply:', err);
      toast.error('Failed to send reply');
      throw err;
    }
  }, [message.id]);

  // Fetch replies when message or filters change
  useEffect(() => {
    fetchReplies();
  }, [fetchReplies]);

  const [showAssignmentModal, setShowAssignmentModal] = useState(false);
  const [showEnhancedAssignmentModal, setShowEnhancedAssignmentModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showDetailedReportModal, setShowDetailedReportModal] = useState(false);
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [activeTab, setActiveTab] = useState<'conversation' | 'timeline' | 'attachments'>('conversation');

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  const [isNearBottom, setIsNearBottom] = useState(true);

  // Simple scroll to bottom when replies change
  useEffect(() => {
    if (replies.length > 0 && isNearBottom) {
      setTimeout(() => scrollToBottom(), 100);
    }
  }, [replies.length, isNearBottom]);
  
  const scrollToBottom = (instant = false) => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      if (instant) {
        container.scrollTop = container.scrollHeight;
      } else {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: "smooth"
        });
      }
    }
  };

  // Handle scroll events
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const container = e.currentTarget;
    const { scrollTop, scrollHeight, clientHeight } = container;

    // Check if user is near bottom (within 100px)
    const nearBottom = scrollHeight - scrollTop - clientHeight < 100;
    setIsNearBottom(nearBottom);
  }, []);

  function formattedTime(timestamp: string | Date | undefined | null) {
    if (!timestamp) {
      return 'Unknown time';
    }

    try {
      const date = new Date(timestamp);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid time';
      }

      return isToday(date) ? format(date, 'h:mm a') : format(date, 'MMM d, h:mm a');
    } catch (error) {
      console.error('Error formatting time:', error);
      return 'Invalid time';
    }
  }

  // Mock timeline events for demonstration
  const timelineEvents: TimelineEvent[] = [
    {
      id: '1',
      type: 'message',
      timestamp: message.timestamp,
      user: { id: 'customer', name: 'Customer', email: '', role: 'viewer', department: '', isActive: true },
      description: 'Initial message received',
      metadata: message.content
    },
    ...(message.assigned_to ? [{
      id: '2',
      type: 'assignment' as const,
      timestamp: new Date(message.timestamp.getTime() + 5 * 60 * 1000),
      user: currentUser!,
      description: `Assigned to ${message.assigned_to.name}`,
      metadata: `Case assigned to ${message.assigned_to.department}`
    }] : []),
    ...replies.map((reply, index) => ({
      id: `reply-${index}`,
      type: 'reply' as const,
      timestamp: reply.timestamp,
      user: reply.sender || { id: 'customer', name: 'Customer', email: '', role: 'viewer', department: '', isActive: true },
      description: reply.direction === 'inbound' ? 'Customer replied' : 'Reply sent to customer',
      metadata: reply.content
    }))
  ];

  const handleSendReply = async (content: string) => {
    try {

      await addReplyToMessage(content);

      // Trigger notification
      window.dispatchEvent(new CustomEvent('newNotification', {
        detail: {
          type: 'message',
          title: 'Reply Sent',
          message: `Reply sent to ${message.phone_number}`,
          caseId: message.caseId
        }
      }));

      // Scroll to bottom to show new reply
      setTimeout(() => scrollToBottom(), 100);
    } catch (error) {
      console.error('Error sending reply:', error);
      // You might want to show an error notification here
    }
  };

  const handleAssignment = (assignment: {
    userId: string;
    comment: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    dueDate: string;
    estimatedHours?: number;
    skillsRequired?: string[];
  }) => {
    // In a real app, you'd find the user by ID
    const mockUser = {
      id: assignment.userId,
      name: 'Assigned User',
      email: '',
      role: 'technician' as const,
      department: 'Field Operations',
      isActive: true
    };

    assignMessage(message.id, mockUser);
    setShowEnhancedAssignmentModal(false);

    // Trigger notification
    window.dispatchEvent(new CustomEvent('newNotification', {
      detail: {
        type: 'assignment',
        title: 'Case Assigned',
        message: `Case ${message.caseId} has been assigned`,
        caseId: message.caseId
      }
    }));
  };

  const handleProgressReport = (report: {
    content: string;
    attachments: File[];
    checklist: any[];
  }) => {
    // In a real app, this would save the progress report
    console.log('Progress report submitted:', report);
    setShowProgressModal(false);

    // Trigger notification
    window.dispatchEvent(new CustomEvent('newNotification', {
      detail: {
        type: 'report',
        title: 'Progress Report',
        message: `Progress report submitted for ${message.caseId}`,
        caseId: message.caseId
      }
    }));
  };

  const handleStatusChange = (newStatus: SMSMessage['status']) => {
    updateMessageStatus(message.id, newStatus);
  };

  const handleApproval = (decision: 'approved' | 'rejected', comments: string) => {
    // In a real app, this would update the approval status
    console.log('Approval decision:', decision, comments);
    setShowApprovalModal(false);
  };

  const handleDetailedReport = (report: any) => {
    // In a real app, this would save the detailed report
    console.log('Detailed report submitted:', report);
    setShowDetailedReportModal(false);
  };

  // Mock approval request for demo
  const mockApprovalRequest = {
    id: '1',
    messageId: message.id,
    type: 'escalation' as const,
    description: 'Request approval for emergency response escalation',
    priority: 'high' as const,
    requestedBy: currentUser!,
    requestedAt: new Date(),
    approvers: [mockUsers[0]],
    status: 'pending' as const,
    approvalHistory: []
  };

  const canAssign = currentUser?.role === 'admin' || currentUser?.role === 'supervisor';
  const canReply = currentUser?.role !== 'viewer';
  const canReport = currentUser?.role === 'technician' || currentUser?.role === 'supervisor';

  console.log('message ***************************', message);
  console.log('replies ***************************', replies);

  return (
    <div className="flex-1 flex flex-col bg-white h-full">
      {/* Modern Header */}
      <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-white to-gray-50">
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-blue-100 rounded-xl">
              <ChatBubbleLeftRightIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{message.phone_number}</h2>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-sm text-gray-500">Case #{message.caseId}</span>
                <span className="text-gray-300">•</span>
                <span className="text-sm text-gray-500">{format(message.timestamp, 'PPpp')}</span>
              </div>
              <div  className="flex items-center gap-10 mt-1">
                  <h4 className="text-sm font-medium text-gray-500">
                    Replies ({replies.length})
                  </h4>
                  {selectedContactMessages && selectedContactMessages.length > 0 && (
                    <h4 className="text-sm font-medium text-gray-500">
                      Conversation ({selectedContactMessages.length} messages)
                    </h4>
                  )}
                  <button
                    onClick={() => setShowReplyFilters(!showReplyFilters)}
                    className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-800"
                  >
                    <FunnelIcon className="h-4 w-4" />
                    Filter
                  </button>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <MessageActions
              message={message}
              onArchive={archiveMessage}
              onDelete={deleteMessage}
              onRestore={restoreMessage}
              onExportPDF={exportToPDF}
              onAddTag={addTag}
            />
          </div>
        </div>

       

      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'conversation', name: 'Conversation', icon: ChatBubbleLeftRightIcon },
            { id: 'timeline', name: 'Timeline', icon: ClockIcon },
            { id: 'attachments', name: 'Attachments', icon: DocumentArrowDownIcon }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${activeTab === tab.id
                  ? 'border-sky-500 text-sky-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
            >
              <tab.icon className="h-4 w-4" />
              {tab.name}
              {tab.id === 'attachments' && message.attachments.length > 0 && (
                <span className="bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                  {message.attachments.length}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="flex-1 flex flex-col overflow-y-auto">
        {/* Original Message */}
            <div className="">
              <div className="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-400">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium text-blue-900">Original Message</div>
                  <div className="text-xs text-blue-600">
                    {format(message.timestamp, 'PPpp')}
                  </div>
                </div>
                <p className="text-blue-800">{message.content}</p>
              </div>
            </div>

        {activeTab === 'conversation' && (
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Reply Filters */}
            <div className="border-b border-gray-200 bg-gray-50">
              <div className="px-4">

                {showReplyFilters && (
                  <div className="space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                     
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Search Content
                        </label>
                        <input
                          type="text"
                          placeholder="Search in replies"
                          value={replyFilters.search}
                          onChange={(e) => setReplyFilters(prev => ({ ...prev, search: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Replies List with Infinite Scroll */}
            <div
              className="flex-1 overflow-y-auto p-4"
              ref={scrollContainerRef}
              onScroll={handleScroll}
            >


              {/* Loading State */}
              {repliesLoading && replies.length === 0 && (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-500">Loading replies...</p>
                </div>
              )}

              {/* Error State */}
              {repliesError && (
                <div className="text-center py-8">
                  <div className="text-red-600 mb-2">Error loading replies</div>
                  <p className="text-gray-500 text-sm">{repliesError}</p>
                </div>
              )}

              {/* Conversation Messages (when contact is selected from MessageList) */}
              {selectedContactMessages && selectedContactMessages.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Conversation History</h3>
                  <div className="space-y-3">
                    {selectedContactMessages.map((msg) => (
                      <div
                        key={msg.id}
                        className={`flex ${
                          msg.direction === 'inbound' ? "justify-start" : "justify-end"
                        } mb-3`}
                      >
                        <div
                          className={`rounded-lg p-3 max-w-xs lg:max-w-md ${
                            msg.direction === 'inbound'
                              ? "bg-gray-50 text-gray-900"
                              : "bg-blue-50 text-blue-900"
                          }`}
                        >
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-xs font-medium">
                              {msg.direction === 'inbound' ? "Customer" : "Agent"}
                            </span>
                            <span className="text-xs text-gray-500">
                              {formattedTime(msg.timestamp)}
                            </span>
                          </div>
                          <p className="text-sm">{msg.content}</p>
                          {msg.status && (
                            <div className="mt-1">
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                msg.status === 'replied' ? 'bg-green-100 text-green-800' :
                                msg.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                                msg.status === 'new' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {msg.status}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  <hr className="my-6" />
                </div>
              )}

              {/* Replies */}
              {replies.map((reply) => (
                <div
                  key={reply.id}
                  className={`flex ${
                    reply.is_from_customer ? "justify-start" : "justify-end"
                  } mb-3`}
                >
                  <div className={`max-w-[80%]`}>
                    <div
                      className={`rounded-lg p-3 ${reply.is_from_customer
                        ? "bg-gray-50 border-l-4 border-gray-400"
                        : "bg-green-50 border-l-4 border-green-400"
                      }`}
                    >
                      <div className="flex justify-between items-start mb-1">
                        <div className="font-medium text-gray-700">
                          {reply.is_from_customer ? "Customer" : reply.sender?.name || "Agent"}
                        </div>
                        <div className="text-xs text-gray-500 ml-2">
                          {formattedTime(reply.timestamp)}
                        </div>
                      </div>
                      <p className="text-gray-800">{reply.content}</p>
                      {reply.attachments && reply.attachments.length > 0 && (
                        <AttachmentViewer attachments={reply.attachments} />
                      )}
                      {!reply.is_from_customer && (
                        <div className="text-right mt-1">
                          <span className="text-xs text-gray-500">
                            {reply.status === "sent" && "✓"}
                            {reply.status === "delivered" && "✓✓"}
                            {(reply.status !== "sent" && reply.status !== "delivered") && (
                              <span>{reply.status}</span>
                            )}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* Empty State */}
              {!repliesLoading && replies.length === 0 && !repliesError && (
                <div className="text-center py-8">
                  <ChatBubbleLeftRightIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No replies yet</p>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Scroll to Bottom Button */}
            {!isNearBottom && replies.length > 0 && (
              <div className="absolute bottom-20 right-4">
                <button
                  onClick={() => scrollToBottom(false)}
                  className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-lg transition-colors"
                  title="Scroll to bottom"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                  </svg>
                </button>
              </div>
            )}


            {/* Reply Box */}
            {canReply && message.status !== 'closed' && (
              <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Send Reply</h3>
                <ReplyBox onSend={handleSendReply} category={message.category} />
              </div>
            )}
          </div>
        )}

        {activeTab === 'timeline' && (
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Case Timeline</h3>
            <CaseTimeline events={timelineEvents} />
          </div>
        )}

        {activeTab === 'attachments' && (
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Attachments</h3>
            {message.attachments.length > 0 ? (
              <AttachmentViewer attachments={message.attachments} />
            ) : (
              <div className="text-center py-8 text-gray-500">
                <DocumentArrowDownIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No attachments</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      <EnhancedAssignmentModal
        isOpen={showEnhancedAssignmentModal}
        onClose={() => setShowEnhancedAssignmentModal(false)}
        onAssign={handleAssignment}
        currentAssignee={message.assigned_to}
        messageId={message.id}
        caseId={message.caseId}
      />

      <ApprovalModal
        isOpen={showApprovalModal}
        onClose={() => setShowApprovalModal(false)}
        onApprove={handleApproval}
        message={message}
        approvalRequest={mockApprovalRequest}
      />

      <DetailedReportModal
        isOpen={showDetailedReportModal}
        onClose={() => setShowDetailedReportModal(false)}
        onSubmit={handleDetailedReport}
        message={message}
      />

      <ProgressReportModal
        isOpen={showProgressModal}
        onClose={() => setShowProgressModal(false)}
        onSubmit={handleProgressReport}
        caseId={message.caseId}
      />
    </div>
  );
}