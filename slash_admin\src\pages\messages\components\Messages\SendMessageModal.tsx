import React, { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { XMarkIcon, PaperAirplaneIcon, UserIcon } from '@heroicons/react/24/outline';
import messageService from '../../../../api/services/messageService';
import { Contact } from '../../../../types';

interface SendMessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSend: (data: any) => void;
  prefilledContact?: Contact;
}

export default function SendMessageModal({ 
  isOpen, 
  onClose, 
  onSend,
  prefilledContact 
}: SendMessageModalProps) {
  const [formData, setFormData] = useState({
    dest_addr: prefilledContact?.phone_number || '',
    content: '',
    source_addr: '',
    priority: 'normal'
  });
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [showContactSuggestions, setShowContactSuggestions] = useState(false);
  const [loading, setLoading] = useState(false);

  // Fetch contacts for suggestions
  useEffect(() => {
    const fetchContacts = async () => {
      try {
        const response = await messageService.getContacts({ page_size: 100 });
        setContacts(response.results || []);
      } catch (error) {
        console.error('Error fetching contacts:', error);
      }
    };

    if (isOpen) {
      fetchContacts();
    }
  }, [isOpen]);

  // Filter contacts based on phone number input
  useEffect(() => {
    if (formData.dest_addr && contacts.length > 0) {
      const filtered = contacts.filter(contact =>
        contact?.phone_number.includes(formData.dest_addr) ||
        contact?.name.toLowerCase().includes(formData.dest_addr.toLowerCase())
      );
      setFilteredContacts(filtered);
      setShowContactSuggestions(filtered.length > 0 && formData.dest_addr.length > 2);
    } else {
      setFilteredContacts([]);
      setShowContactSuggestions(false);
    }
  }, [formData.dest_addr, contacts]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.dest_addr || !formData.content) {
      return;
    }

    setLoading(true);
    try {
      await onSend(formData);
      setFormData({
        dest_addr: '',
        content: '',
        source_addr: '',
        priority: 'normal'
      });
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setLoading(false);
    }
  };

  const selectContact = (contact: Contact) => {
    setFormData(prev => ({
      ...prev,
      dest_addr: contact?.phone_number
    }));
    setShowContactSuggestions(false);
  };

  const getCharacterCount = () => {
    const length = formData.content.length;
    const smsLength = 160; // Standard SMS length
    const parts = Math.ceil(length / smsLength);
    return { length, parts };
  };

  const { length: charCount, parts: smsParts } = getCharacterCount();

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-4">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                    Send SMS Message
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Recipient */}
                  <div className="relative">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Recipient Phone Number
                    </label>
                    <input
                      type="text"
                      value={formData.dest_addr}
                      onChange={(e) => setFormData(prev => ({ ...prev, dest_addr: e.target.value }))}
                      placeholder="e.g., 0911234567"
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                    
                    {/* Contact suggestions */}
                    {showContactSuggestions && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto">
                        {filteredContacts.map((contact) => (
                          <div
                            key={contact.id}
                            onClick={() => selectContact(contact)}
                            className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                          >
                            <div className="flex items-center space-x-2">
                              <UserIcon className="h-4 w-4 text-gray-400" />
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {contact?.name}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {contact?.phone_number}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Source address (optional) */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Source Number (Optional)
                    </label>
                    <input
                      type="text"
                      value={formData.source_addr}
                      onChange={(e) => setFormData(prev => ({ ...prev, source_addr: e.target.value }))}
                      placeholder="Leave empty for default"
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  {/* Priority */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Priority
                    </label>
                    <select
                      value={formData.priority}
                      onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value }))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="low">Low</option>
                      <option value="normal">Normal</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </select>
                  </div>

                  {/* Message content */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Message Content
                    </label>
                    <textarea
                      value={formData.content}
                      onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                      placeholder="Type your message here... (Supports Amharic: ሰላም)"
                      rows={4}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                      required
                    />
                    
                    {/* Character count */}
                    <div className="mt-1 flex justify-between text-xs text-gray-500">
                      <span>{charCount} characters</span>
                      <span className={smsParts > 1 ? 'text-orange-600' : 'text-gray-500'}>
                        {smsParts} SMS part{smsParts > 1 ? 's' : ''}
                      </span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={!formData.dest_addr || !formData.content || loading}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                      {loading ? 'Sending...' : 'Send Message'}
                    </button>
                  </div>
                </form>

                {/* Quick templates */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Quick Templates</h4>
                  <div className="space-y-1">
                    {[
                      'እናመሰግናለን። መልእክትዎን ተቀብለናል።',
                      'ችግሩ በቅርቡ ይፈታል። እናመሰግናለን።',
                      'Thank you for contacting us. We will respond shortly.',
                      'Your issue has been resolved. Thank you for your patience.'
                    ].map((template, index) => (
                      <button
                        key={index}
                        onClick={() => setFormData(prev => ({ ...prev, content: template }))}
                        className="w-full text-left text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-50 p-2 rounded"
                      >
                        {template}
                      </button>
                    ))}
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
