
import React, { useState } from 'react';
import {
  ArrowLeft,
  Edit,
  Clock,
  User,
  Calendar,
  FileText,
  CheckSquare,
  Upload,
  MessageSquare,
  AlertTriangle,
  CheckCircle,
  Eye,
  Download,
  Plus,
  X,
  Save
} from 'lucide-react';
import { format } from 'date-fns';
import { Case as CaseType, User as UserType } from '../../types';

interface CaseEvent {
  id: string;
  type: string;
  createdAt: string;
  createdByUser?: UserType;
  data: any;
}

interface Task {
  id: string;
  title: string;
  description?: string;
  status: string;
  type: string;
  assignee?: UserType;
  dueAt?: string;
  checklist?: ChecklistItem[];
}

interface ChecklistItem {
  id: string;
  text: string;
  completed: boolean;
}

interface Document {
  id: string;
  filename: string;
  size: number;
  uploadedByUser?: User;
  createdAt: string;
  isEvidence: boolean;
  description?: string;
  tags: string[];
}

interface Case {
  id: string;
  caseNo: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  createdAt: string;
  updatedAt: string;
  createdByUser?: User;
  assignedToUser?: User;
  reporterType: string;
  reporterContact: {
    name?: string;
    email?: string;
    phone?: string;
  };
  slaFirstResponseDue?: string;
  slaResolutionDue?: string;
  isOverdue: boolean;
  events?: CaseEvent[];
  tasks?: Task[];
  documents?: Document[];
}

interface CaseDetailProps {
  case: CaseType;
  onBack: () => void;
  onCaseUpdate: (updatedCase: CaseType) => void;
}

const caseData = {
  id: 'case-001',
  caseNo: 'CASE-2024-001',
  title: 'Power Outage in Sector 5',
  description: 'Residents in Sector 5 have been experiencing intermittent power outages for the past 48 hours. The issue seems to be affecting the entire neighborhood and requires immediate attention from the utility team.',
  status: 'Investigating',
  priority: 'High',
  category: 'power-outage',
  createdAt: '2024-01-15T08:30:00Z',
  updatedAt: '2024-01-16T14:45:00Z',
  createdByUser: {
    id: 'user-001',
    name: 'John Smith',
    email: '<EMAIL>'
  },
  assignedToUser: {
    id: 'user-002',
    name: 'Maria Garcia',
    email: '<EMAIL>'
  },
  reporterType: 'Resident',
  reporterContact: {
    name: 'Ahmed Hassan',
    email: '<EMAIL>',
    phone: '+251911223344'
  },
  slaFirstResponseDue: '2024-01-15T10:30:00Z',
  slaResolutionDue: '2024-01-17T18:00:00Z',
  isOverdue: false,
  events: [
    {
      id: 'event-001',
      type: 'StatusChange',
      createdAt: '2024-01-15T08:35:00Z',
      createdByUser: {
        id: 'user-001',
        name: 'John Smith',
        email: '<EMAIL>'
      },
      data: {
        oldValue: 'New',
        newValue: 'UnderReview'
      }
    },
    {
      id: 'event-002',
      type: 'StatusChange',
      createdAt: '2024-01-15T09:45:00Z',
      createdByUser: {
        id: 'user-002',
        name: 'Maria Garcia',
        email: '<EMAIL>'
      },
      data: {
        oldValue: 'UnderReview',
        newValue: 'Investigating'
      }
    },
    {
      id: 'event-003',
      type: 'Note',
      createdAt: '2024-01-15T11:20:00Z',
      createdByUser: {
        id: 'user-002',
        name: 'Maria Garcia',
        email: '<EMAIL>'
      },
      data: {
        note: 'Dispatched field team to investigate the transformer in Sector 5. Initial assessment suggests overload during peak hours.'
      }
    },
    {
      id: 'event-004',
      type: 'Assignment',
      createdAt: '2024-01-15T08:40:00Z',
      createdByUser: {
        id: 'user-001',
        name: 'John Smith',
        email: '<EMAIL>'
      },
      data: {
        oldValue: 'Unassigned',
        newValue: 'Maria Garcia'
      }
    }
  ],
  tasks: [
    {
      id: 'task-001',
      title: 'Inspect Transformer T-501',
      description: 'Perform detailed inspection of transformer T-501 in Sector 5 to identify the cause of intermittent outages.',
      status: 'InProgress',
      type: 'Investigation',
      assignee: {
        id: 'user-003',
        name: 'David Kim',
        email: '<EMAIL>'
      },
      dueAt: '2024-01-16T17:00:00Z',
      checklist: [
        {
          id: 'check-001',
          text: 'Check oil levels and quality',
          completed: true
        },
        {
          id: 'check-002',
          text: 'Inspect cooling system',
          completed: true
        },
        {
          id: 'check-003',
          text: 'Test load capacity',
          completed: false
        },
        {
          id: 'check-004',
          text: 'Document findings with photos',
          completed: false
        }
      ]
    },
    {
      id: 'task-002',
      title: 'Notify affected residents',
      description: 'Send SMS notifications to residents in Sector 5 about the ongoing investigation and expected resolution time.',
      status: 'Done',
      type: 'Communication',
      assignee: {
        id: 'user-001',
        name: 'John Smith',
        email: '<EMAIL>'
      },
      dueAt: '2024-01-15T12:00:00Z'
    }
  ],
  documents: [
    {
      id: 'doc-001',
      filename: 'sector5_outage_report.pdf',
      size: 2457600,
      uploadedByUser: {
        id: 'user-002',
        name: 'Maria Garcia',
        email: '<EMAIL>'
      },
      createdAt: '2024-01-15T14:30:00Z',
      isEvidence: true,
      description: 'Initial assessment report of the power outage in Sector 5',
      tags: ['assessment', 'transformer', 'sector5']
    },
    {
      id: 'doc-002',
      filename: 'transformer_specs.docx',
      size: 512000,
      uploadedByUser: {
        id: 'user-003',
        name: 'David Kim',
        email: '<EMAIL>'
      },
      createdAt: '2024-01-16T09:15:00Z',
      isEvidence: false,
      description: 'Technical specifications for transformer T-501',
      tags: ['specifications', 'technical']
    }
  ]
};

const CaseDetail: React.FC<CaseDetailProps> = ({ case: caseData1, onBack, onCaseUpdate }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [showNoteModal, setShowNoteModal] = useState(false);
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [newStatus, setNewStatus] = useState(caseData.status);
  const [newNote, setNewNote] = useState('');
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    type: 'General',
    dueDate: '',
  });
  const [localCase, setLocalCase] = useState<Case>(caseData);

  // Update local case when prop changes
  React.useEffect(() => {
    setLocalCase(caseData);
  }, [caseData]);

  const tabs = [
    { id: 'overview', name: 'Overview', icon: FileText },
    { id: 'timeline', name: 'Timeline', icon: Clock },
    { id: 'tasks', name: 'Tasks', icon: CheckSquare },
    { id: 'documents', name: 'Documents', icon: Upload },
    { id: 'notes', name: 'Notes', icon: MessageSquare },
  ];

  const statusOptions = [
    'New',
    'UnderReview',
    'Investigating',
    'AwaitingAction',
    'Resolved',
    'Closed',
    'Archived'
  ];

  const taskTypes = [
    'General',
    'Investigation',
    'Communication',
    'Documentation',
    'FollowUp'
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'New': return 'bg-blue-100 text-blue-800';
      case 'UnderReview': return 'bg-yellow-100 text-yellow-800';
      case 'Investigating': return 'bg-orange-100 text-orange-800';
      case 'AwaitingAction': return 'bg-purple-100 text-purple-800';
      case 'Resolved': return 'bg-green-100 text-green-800';
      case 'Closed': return 'bg-gray-100 text-gray-800';
      case 'Archived': return 'bg-gray-100 text-gray-600';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Compliance': return 'bg-red-100 text-red-800';
      case 'Complaint': return 'bg-blue-100 text-blue-800';
      case 'Audit': return 'bg-purple-100 text-purple-800';
      case 'Incident': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTaskStatusIcon = (status: string) => {
    switch (status) {
      case 'Done': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'InProgress': return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'Blocked': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default: return <CheckSquare className="w-4 h-4 text-gray-500" />;
    }
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'StatusChange': return <CheckCircle className="w-4 h-4 text-blue-500" />;
      case 'Assignment': return <User className="w-4 h-4 text-green-500" />;
      case 'Note': return <MessageSquare className="w-4 h-4 text-gray-500" />;
      case 'Upload': return <Upload className="w-4 h-4 text-purple-500" />;
      default: return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleStatusUpdate = () => {
    const updatedCase = {
      ...localCase,
      status: newStatus,
      updatedAt: new Date().toISOString(),
      events: [
        ...(localCase.events || []),
        {
          id: `event-${Date.now()}`,
          type: 'StatusChange',
          createdAt: new Date().toISOString(),
          createdByUser: { id: 'user-1', name: 'Current User', email: '<EMAIL>' },
          data: {
            oldValue: localCase.status,
            newValue: newStatus,
          },
        }
      ]
    };
    
    setLocalCase(updatedCase);
    onCaseUpdate(updatedCase);
    setShowStatusModal(false);
  };

  const handleAddNote = () => {
    if (!newNote.trim()) return;
    
    const updatedCase = {
      ...localCase,
      updatedAt: new Date().toISOString(),
      events: [
        ...(localCase.events || []),
        {
          id: `event-${Date.now()}`,
          type: 'Note',
          createdAt: new Date().toISOString(),
          createdByUser: { id: 'user-1', name: 'Current User', email: '<EMAIL>' },
          data: {
            note: newNote,
          },
        }
      ]
    };
    
    setLocalCase(updatedCase);
    onCaseUpdate(updatedCase);
    setNewNote('');
    setShowNoteModal(false);
  };

  const handleAddTask = () => {
    if (!newTask.title.trim()) return;
    
    const updatedCase = {
      ...localCase,
      updatedAt: new Date().toISOString(),
      tasks: [
        ...(localCase.tasks || []),
        {
          id: `task-${Date.now()}`,
          title: newTask.title,
          description: newTask.description,
          status: 'Todo',
          type: newTask.type,
          dueAt: newTask.dueDate,
        }
      ]
    };
    
    setLocalCase(updatedCase);
    onCaseUpdate(updatedCase);
    setNewTask({
      title: '',
      description: '',
      type: 'General',
      dueDate: '',
    });
    setShowTaskModal(false);
  };

  const handleTaskStatusChange = (taskId: string, newStatus: string) => {
    const updatedCase = {
      ...localCase,
      updatedAt: new Date().toISOString(),
      tasks: (localCase.tasks || []).map(task => 
        task.id === taskId ? { ...task, status: newStatus } : task
      )
    };
    
    setLocalCase(updatedCase);
    onCaseUpdate(updatedCase);
  };

  const OverviewTab: React.FC = () => (
    <div className="space-y-6">
      {/* Case Information */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Case Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700">Case Number</label>
            <p className="mt-1 text-sm text-gray-900 font-mono">{localCase.caseNo}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Category</label>
            <span className={`mt-1 inline-block px-2 py-1 text-xs font-medium rounded-full ${getCategoryColor(localCase.category)}`}>
              {localCase.category}
            </span>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Priority</label>
            <span className={`mt-1 inline-block px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(localCase.priority)}`}>
              {localCase.priority}
            </span>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Status</label>
            <span className={`mt-1 inline-block px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(localCase.status)}`}>
              {localCase.status.replace(/([A-Z])/g, ' $1').trim()}
            </span>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Created By</label>
            <p className="mt-1 text-sm text-gray-900">{localCase.createdByUser?.name || 'Unknown'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Assigned To</label>
            <p className="mt-1 text-sm text-gray-900">
              {localCase.assignedToUser?.name || 'Unassigned'}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Created</label>
            <p className="mt-1 text-sm text-gray-900">
              {format(new Date(localCase.createdAt), 'MMM dd, yyyy HH:mm')}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Last Updated</label>
            <p className="mt-1 text-sm text-gray-900">
              {format(new Date(localCase.updatedAt), 'MMM dd, yyyy HH:mm')}
            </p>
          </div>
        </div>
      </div>

      {/* Description */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Description</h3>
        <p className="text-sm text-gray-700 leading-relaxed">{localCase.description}</p>
      </div>

      {/* Reporter Information */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Reporter Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700">Reporter Type</label>
            <p className="mt-1 text-sm text-gray-900">{localCase.reporterType}</p>
          </div>
          {localCase.reporterContact.name && (
            <div>
              <label className="block text-sm font-medium text-gray-700">Name</label>
              <p className="mt-1 text-sm text-gray-900">{localCase.reporterContact.name}</p>
            </div>
          )}
          {localCase.reporterContact.email && (
            <div>
              <label className="block text-sm font-medium text-gray-700">Email</label>
              <p className="mt-1 text-sm text-gray-900">{localCase.reporterContact.email}</p>
            </div>
          )}
          {localCase.reporterContact.phone && (
            <div>
              <label className="block text-sm font-medium text-gray-700">Phone</label>
              <p className="mt-1 text-sm text-gray-900">{localCase.reporterContact.phone}</p>
            </div>
          )}
        </div>
      </div>

      {/* SLA Information */}
      {(localCase.slaFirstResponseDue || localCase.slaResolutionDue) && (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">SLA Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {localCase.slaFirstResponseDue && (
              <div>
                <label className="block text-sm font-medium text-gray-700">First Response Due</label>
                <p className={`mt-1 text-sm ${localCase.isOverdue ? 'text-red-600 font-medium' : 'text-gray-900'}`}>
                  {format(new Date(localCase.slaFirstResponseDue), 'MMM dd, yyyy HH:mm')}
                  {localCase.isOverdue && ' (Overdue)'}
                </p>
              </div>
            )}
            {localCase.slaResolutionDue && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Resolution Due</label>
                <p className={`mt-1 text-sm ${localCase.isOverdue ? 'text-red-600 font-medium' : 'text-gray-900'}`}>
                  {format(new Date(localCase.slaResolutionDue), 'MMM dd, yyyy HH:mm')}
                  {localCase.isOverdue && ' (Overdue)'}
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );

  const TimelineTab: React.FC = () => (
    <div className="space-y-4">
      {localCase.events?.map((event) => (
        <div key={event.id} className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 mt-1">
              {getEventIcon(event.type)}
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-gray-900">
                  {event.type.replace(/([A-Z])/g, ' $1').trim()}
                </h4>
                <span className="text-xs text-gray-500">
                  {format(new Date(event.createdAt), 'MMM dd, yyyy HH:mm')}
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                By {event.createdByUser?.name || 'Unknown'}
              </p>
              {event.data.note && (
                <p className="text-sm text-gray-700 mt-2">{event.data.note}</p>
              )}
              {event.data.oldValue && event.data.newValue && (
                <div className="text-xs text-gray-500 mt-2">
                  Changed from <span className="font-medium">{event.data.oldValue}</span> to{' '}
                  <span className="font-medium">{event.data.newValue}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const TasksTab: React.FC = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Tasks</h3>
        <button 
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          onClick={() => setShowTaskModal(true)}
        >
          <Plus className="w-4 h-4" />
          <span>New Task</span>
        </button>
      </div>
      
      {localCase.tasks?.map((task) => (
        <div key={task.id} className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-1">
                {getTaskStatusIcon(task.status)}
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-medium text-gray-900">{task.title}</h4>
                {task.description && (
                  <p className="text-sm text-gray-600 mt-1">{task.description}</p>
                )}
                <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                  <span>Type: {task.type.replace(/([A-Z])/g, ' $1').trim()}</span>
                  {task.assignee && <span>Assigned to: {task.assignee.name}</span>}
                  {task.dueAt && (
                    <span>Due: {format(new Date(task.dueAt), 'MMM dd, yyyy')}</span>
                  )}
                </div>
                {task.checklist && task.checklist.length > 0 && (
                  <div className="mt-3">
                    <div className="text-xs text-gray-500 mb-2">
                      Checklist ({task.checklist.filter(item => item.completed).length}/{task.checklist.length} completed)
                    </div>
                    <div className="space-y-1">
                      {task.checklist.map((item) => (
                        <div key={item.id} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={item.completed}
                            readOnly
                            className="h-3 w-3 text-blue-600 border-gray-300 rounded"
                          />
                          <span className={`text-xs ${item.completed ? 'text-gray-500 line-through' : 'text-gray-700'}`}>
                            {item.text}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="flex flex-col items-end space-y-2">
              <select
                value={task.status}
                onChange={(e) => handleTaskStatusChange(task.id, e.target.value)}
                className={`px-2 py-1 text-xs font-medium rounded-full ${
                  task.status === 'Done' ? 'bg-green-100 text-green-800' :
                  task.status === 'InProgress' ? 'bg-yellow-100 text-yellow-800' :
                  task.status === 'Blocked' ? 'bg-red-100 text-red-800' :
                  'bg-gray-100 text-gray-800'
                }`}
              >
                <option value="Todo">To Do</option>
                <option value="InProgress">In Progress</option>
                <option value="Blocked">Blocked</option>
                <option value="Done">Done</option>
              </select>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const DocumentsTab: React.FC = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Documents & Evidence</h3>
        <button 
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          onClick={() => setShowDocumentModal(true)}
        >
          <Upload className="w-4 h-4" />
          <span>Upload Document</span>
        </button>
      </div>
      
      {localCase.documents?.map((document) => (
        <div key={document.id} className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FileText className="w-5 h-5 text-gray-400" />
              <div>
                <h4 className="text-sm font-medium text-gray-900">{document.filename}</h4>
                <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                  <span>{formatFileSize(document.size)}</span>
                  <span>Uploaded by {document.uploadedByUser?.name || 'Unknown'}</span>
                  <span>{format(new Date(document.createdAt), 'MMM dd, yyyy')}</span>
                  {document.isEvidence && (
                    <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full font-medium">
                      Evidence
                    </span>
                  )}
                </div>
                {document.description && (
                  <p className="text-sm text-gray-600 mt-1">{document.description}</p>
                )}
                {document.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {document.tags.map((tag, index) => (
                      <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-1 text-gray-400 hover:text-gray-600">
                <Eye className="w-4 h-4" />
              </button>
              <button className="p-1 text-gray-400 hover:text-gray-600">
                <Download className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const NotesTab: React.FC = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Case Notes</h3>
        <button 
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          onClick={() => setShowNoteModal(true)}
        >
          <Plus className="w-4 h-4" />
          <span>Add Note</span>
        </button>
      </div>
      
      {localCase.events?.filter(event => event.type === 'Note').map((event) => (
        <div key={event.id} className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-start space-x-3">
            <MessageSquare className="w-5 h-5 text-gray-400 mt-1" />
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">
                  {event.createdByUser?.name || 'Unknown'}
                </span>
                <span className="text-xs text-gray-500">
                  {format(new Date(event.createdAt), 'MMM dd, yyyy HH:mm')}
                </span>
              </div>
              <p className="text-sm text-gray-700 mt-2">{event.data.note}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  // Modal components
  const StatusModal: React.FC = () => (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Update Case Status</h3>
          <button onClick={() => setShowStatusModal(false)} className="text-gray-400 hover:text-gray-600">
            <X className="w-5 h-5" />
          </button>
        </div>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select
              value={newStatus}
              onChange={(e) => setNewStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {statusOptions.map(option => (
                <option key={option} value={option}>
                  {option.replace(/([A-Z])/g, ' $1').trim()}
                </option>
              ))}
            </select>
          </div>
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowStatusModal(false)}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleStatusUpdate}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Update Status
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const NoteModal: React.FC = () => (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Add Note</h3>
          <button onClick={() => setShowNoteModal(false)} className="text-gray-400 hover:text-gray-600">
            <X className="w-5 h-5" />
          </button>
        </div>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Note</label>
            <textarea
              value={newNote}
              onChange={(e) => setNewNote(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter your note here..."
            />
          </div>
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowNoteModal(false)}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleAddNote}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Add Note
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const TaskModal: React.FC = () => (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Add New Task</h3>
          <button onClick={() => setShowTaskModal(false)} className="text-gray-400 hover:text-gray-600">
            <X className="w-5 h-5" />
          </button>
        </div>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
            <input
              type="text"
              value={newTask.title}
              onChange={(e) => setNewTask({...newTask, title: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Task title"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
            <textarea
              value={newTask.description}
              onChange={(e) => setNewTask({...newTask, description: e.target.value})}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Task description"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
              <select
                value={newTask.type}
                onChange={(e) => setNewTask({...newTask, type: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {taskTypes.map(type => (
                  <option key={type} value={type}>
                    {type.replace(/([A-Z])/g, ' $1').trim()}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Due Date</label>
              <input
                type="date"
                value={newTask.dueDate}
                onChange={(e) => setNewTask({...newTask, dueDate: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowTaskModal(false)}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleAddTask}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Add Task
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const DocumentModal: React.FC = () => (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Upload Document</h3>
          <button onClick={() => setShowDocumentModal(false)} className="text-gray-400 hover:text-gray-600">
            <X className="w-5 h-5" />
          </button>
        </div>
        <div className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-sm text-gray-600">Drag and drop files here, or click to select files</p>
            <input type="file" className="hidden" id="file-upload" />
            <label htmlFor="file-upload" className="mt-2 inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
              Select Files
            </label>
          </div>
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowDocumentModal(false)}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={() => setShowDocumentModal(false)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Upload
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Cases</span>
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{localCase.caseNo}</h1>
            <p className="text-gray-600">{localCase.title}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          {localCase.isOverdue && (
            <div className="flex items-center space-x-1 text-red-600">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-sm font-medium">Overdue</span>
            </div>
          )}
          <button
            onClick={() => setShowStatusModal(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Edit className="w-4 h-4" />
            <span>Update Status</span>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'overview' && <OverviewTab />}
        {activeTab === 'timeline' && <TimelineTab />}
        {activeTab === 'tasks' && <TasksTab />}
        {activeTab === 'documents' && <DocumentsTab />}
        {activeTab === 'notes' && <NotesTab />}
      </div>

      {/* Modals */}
      {showStatusModal && <StatusModal />}
      {showNoteModal && <NoteModal />}
      {showTaskModal && <TaskModal />}
      {showDocumentModal && <DocumentModal />}
    </div>
  );
};

export default CaseDetail;