import React, { useState } from 'react';
import messageService from '../api/services/messageService';

export default function ApiTest() {
  const [response, setResponse] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testApi = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Testing API...');

      // First test basic connectivity
      console.log('Testing basic connectivity to:', import.meta.env.VITE_APP_BASE_API);

      const result = await messageService.getMessages({
        page: 1,
        page_size: 5,
        archived: false,
        // ordering: '-timestamp'
      });

      console.log('API Test Result:', result);
      setResponse(result);
    } catch (err: any) {
      console.error('API Test Error:', err);
      console.error('Error stack:', err.stack);

      let errorMessage = 'Unknown error';
      if (err.response) {
        errorMessage = `HTTP ${err.response.status}: ${err.response.statusText}`;
        if (err.response.data) {
          errorMessage += ` - ${JSON.stringify(err.response.data)}`;
        }
      } else if (err.request) {
        errorMessage = 'No response received from server';
      } else {
        errorMessage = err.message || 'Request setup error';
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const testConnectivity = async () => {
    try {
      setLoading(true);
      setError(null);

      // Test basic connectivity with a simple fetch
      const baseUrl = import.meta.env.VITE_APP_BASE_API;
      console.log('Testing connectivity to:', baseUrl);

      const response = await fetch(`${baseUrl}/messages/?page=1&page_size=1`);
      console.log('Fetch response:', response);

      if (response.ok) {
        const data = await response.json();
        console.log('Fetch data:', data);
        setResponse({ connectivity: 'OK', data });
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (err: any) {
      console.error('Connectivity test error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-bold mb-4">API Test</h3>
      
      <div className="space-x-2">
        <button
          onClick={testConnectivity}
          disabled={loading}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Connectivity'}
        </button>

        <button
          onClick={testApi}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Messages API'}
        </button>
      </div>

      {error && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <strong>Error:</strong> {error}
        </div>
      )}

      {response && (
        <div className="mt-4">
          <h4 className="font-semibold mb-2">Response:</h4>
          <pre className="bg-white p-3 border rounded text-sm overflow-auto max-h-96">
            {JSON.stringify(response, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
