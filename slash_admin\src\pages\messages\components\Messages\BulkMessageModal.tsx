import React, { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { 
  XMarkIcon, 
  PaperAirplaneIcon, 
  UserGroupIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import messageService from '../../../../api/services/messageService';
import { Contact } from '../../../../types';

interface BulkMessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSend: (data: any) => void;
}

export default function BulkMessageModal({ 
  isOpen, 
  onClose, 
  onSend 
}: BulkMessageModalProps) {
  const [formData, setFormData] = useState({
    recipients: [] as string[],
    content: '',
    source_addr: '',
    priority: 'normal'
  });
  const [recipientInput, setRecipientInput] = useState('');
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [selectedContacts, setSelectedContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [inputMethod, setInputMethod] = useState<'manual' | 'contacts'>('manual');

  // Fetch contacts
  useEffect(() => {
    const fetchContacts = async () => {
      try {
        const response = await messageService.getContacts({ 
          page_size: 200,
          opt_in_status: true // Only opted-in contacts
        });
        setContacts(response.results || []);
      } catch (error) {
        console.error('Error fetching contacts:', error);
      }
    };

    if (isOpen) {
      fetchContacts();
    }
  }, [isOpen]);

  const handleAddRecipients = () => {
    const numbers = recipientInput
      .split(/[,\n]/)
      .map(num => num.trim())
      .filter(num => num.length > 0);
    
    const validNumbers = numbers.filter(num => /^09\d{8}$/.test(num));
    const invalidNumbers = numbers.filter(num => !/^09\d{8}$/.test(num));
    
    if (invalidNumbers.length > 0) {
      alert(`Invalid numbers: ${invalidNumbers.join(', ')}`);
    }
    
    setFormData(prev => ({
      ...prev,
      recipients: [...new Set([...prev.recipients, ...validNumbers])]
    }));
    setRecipientInput('');
  };

  const handleContactSelection = (contact: Contact, selected: boolean) => {
    if (selected) {
      setSelectedContacts(prev => [...prev, contact]);
      setFormData(prev => ({
        ...prev,
        recipients: [...new Set([...prev.recipients, contact?.phone_number])]
      }));
    } else {
      setSelectedContacts(prev => prev.filter(c => c.id !== contact.id));
      setFormData(prev => ({
        ...prev,
        recipients: prev.recipients.filter(num => num !== contact?.phone_number)
      }));
    }
  };

  const removeRecipient = (phoneNumber: string) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.filter(num => num !== phoneNumber)
    }));
    setSelectedContacts(prev => prev.filter(c => c.phone_number !== phoneNumber));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.recipients.length === 0 || !formData.content) {
      return;
    }

    setLoading(true);
    try {
      await onSend(formData);
      setFormData({
        recipients: [],
        content: '',
        source_addr: '',
        priority: 'normal'
      });
      setSelectedContacts([]);
      setRecipientInput('');
    } catch (error) {
      console.error('Error sending bulk message:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCharacterCount = () => {
    const length = formData.content.length;
    const smsLength = 160;
    const parts = Math.ceil(length / smsLength);
    return { length, parts };
  };

  const { length: charCount, parts: smsParts } = getCharacterCount();
  const estimatedCost = formData.recipients.length * smsParts;

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-4">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900 flex items-center space-x-2">
                    <UserGroupIcon className="h-6 w-6" />
                    <span>Send Bulk SMS</span>
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Input method selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Recipient Selection Method
                    </label>
                    <div className="flex space-x-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          value="manual"
                          checked={inputMethod === 'manual'}
                          onChange={(e) => setInputMethod(e.target.value as 'manual' | 'contacts')}
                          className="mr-2"
                        />
                        Manual Entry
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          value="contacts"
                          checked={inputMethod === 'contacts'}
                          onChange={(e) => setInputMethod(e.target.value as 'manual' | 'contacts')}
                          className="mr-2"
                        />
                        Select from Contacts
                      </label>
                    </div>
                  </div>

                  {/* Manual entry */}
                  {inputMethod === 'manual' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Phone Numbers (one per line or comma-separated)
                      </label>
                      <textarea
                        value={recipientInput}
                        onChange={(e) => setRecipientInput(e.target.value)}
                        placeholder="0911234567&#10;0922345678&#10;0933456789"
                        rows={4}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      <button
                        type="button"
                        onClick={handleAddRecipients}
                        className="mt-2 text-sm text-blue-600 hover:text-blue-800"
                      >
                        Add Numbers
                      </button>
                    </div>
                  )}

                  {/* Contact selection */}
                  {inputMethod === 'contacts' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Select Contacts ({contacts.length} available)
                      </label>
                      <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-md">
                        {contacts.map((contact) => (
                          <label
                            key={contact.id}
                            className="flex items-center p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                          >
                            <input
                              type="checkbox"
                              checked={selectedContacts.some(c => c.id === contact.id)}
                              onChange={(e) => handleContactSelection(contact, e.target.checked)}
                              className="mr-3"
                            />
                            <div className="flex-1">
                              <div className="text-sm font-medium text-gray-900">
                                {contact?.name}
                              </div>
                              <div className="text-xs text-gray-500">
                                {contact?.phone_number} • {contact?.location}
                              </div>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Selected recipients */}
                  {formData.recipients.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Selected Recipients ({formData.recipients.length})
                      </label>
                      <div className="max-h-32 overflow-y-auto border border-gray-300 rounded-md p-2">
                        <div className="flex flex-wrap gap-1">
                          {formData.recipients.map((number) => (
                            <span
                              key={number}
                              className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800"
                            >
                              {number}
                              <button
                                type="button"
                                onClick={() => removeRecipient(number)}
                                className="ml-1 text-blue-600 hover:text-blue-800"
                              >
                                <XMarkIcon className="h-3 w-3" />
                              </button>
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Message content */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Message Content
                    </label>
                    <textarea
                      value={formData.content}
                      onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                      placeholder="Type your bulk message here... (Supports Amharic)"
                      rows={4}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                      required
                    />
                    
                    {/* Message stats */}
                    <div className="mt-2 grid grid-cols-3 gap-4 text-xs">
                      <div className="text-center p-2 bg-gray-50 rounded">
                        <div className="font-medium text-gray-900">{charCount}</div>
                        <div className="text-gray-500">Characters</div>
                      </div>
                      <div className="text-center p-2 bg-gray-50 rounded">
                        <div className={`font-medium ${smsParts > 1 ? 'text-orange-600' : 'text-gray-900'}`}>
                          {smsParts}
                        </div>
                        <div className="text-gray-500">SMS Parts</div>
                      </div>
                      <div className="text-center p-2 bg-gray-50 rounded">
                        <div className="font-medium text-gray-900">{estimatedCost}</div>
                        <div className="text-gray-500">Total SMS</div>
                      </div>
                    </div>
                  </div>

                  {/* Warning for large sends */}
                  {estimatedCost > 100 && (
                    <div className="flex items-center space-x-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
                      <span className="text-sm text-yellow-800">
                        Large bulk send: {estimatedCost} SMS messages will be sent
                      </span>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={formData.recipients.length === 0 || !formData.content || loading}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                      {loading ? 'Sending...' : `Send to ${formData.recipients.length} Recipients`}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
