import React from 'react';
import { Contact } from '../../../../types';
import { 
  UserIcon, 
  PhoneIcon, 
  MapPinIcon,
  ChatBubbleLeftRightIcon,
  ClipboardDocumentListIcon,
  ClockIcon,
  CheckCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface ContactListProps {
  contacts: Contact[];
  onSelectContact: (contact: Contact) => void;
  selectedContactId?: string;
  loading?: boolean;
}

export default function ContactList({ 
  contacts, 
  onSelectContact, 
  selectedContactId,
  loading 
}: ContactListProps) {
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      return `${Math.floor(diffInHours * 60)}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getLanguageFlag = (language: string) => {
    switch (language) {
      case 'am': return '🇪🇹'; // Amharic
      case 'or': return '🇪🇹'; // Oromo
      case 'ti': return '🇪🇹'; // Tigrinya
      case 'so': return '🇸🇴'; // Somali
      case 'en': return '🇺🇸'; // English
      default: return '🌐';
    }
  };

  if (loading) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="bg-white p-4 rounded-lg shadow">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4">
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          Contacts ({contacts.length})
        </h2>
        
        {contacts.length === 0 ? (
          <div className="text-center py-8">
            <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No contacts</h3>
            <p className="mt-1 text-sm text-gray-500">
              Contacts will appear here when customers send messages.
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {contacts.map((contact) => (
              <div
                key={contact.id}
                onClick={() => onSelectContact(contact)}
                className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                  selectedContactId === contact.id
                    ? 'bg-blue-50 border-blue-200'
                    : 'bg-white border-gray-200 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1 min-w-0">
                    {/* Avatar */}
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                        <UserIcon className="h-6 w-6 text-gray-400" />
                      </div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      {/* Name and phone */}
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {contact?.name}
                        </h3>
                        <span className="text-xs text-gray-500 flex items-center space-x-1">
                          {getLanguageFlag(contact.preferred_language)}
                          <span>{contact.preferred_language.toUpperCase()}</span>
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-1 text-sm text-gray-600 mb-2">
                        <PhoneIcon className="h-4 w-4 text-gray-400" />
                        <span>{contact?.phone_number}</span>
                      </div>
                      
                      {/* Location */}
                      {contact?.location && (
                        <div className="flex items-center space-x-1 text-sm text-gray-600 mb-2">
                          <MapPinIcon className="h-4 w-4 text-gray-400" />
                          <span className="truncate">{contact?.location}</span>
                        </div>
                      )}
                      
                      {/* Company */}
                      {contact.company && (
                        <div className="text-sm text-gray-600 mb-2">
                          <span className="truncate">{contact.company}</span>
                        </div>
                      )}
                      
                      {/* Stats */}
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <div className="flex items-center space-x-1">
                          <ChatBubbleLeftRightIcon className="h-3 w-3" />
                          <span>{contact.message_count} messages</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <ClipboardDocumentListIcon className="h-3 w-3" />
                          <span>{contact.case_count} cases</span>
                        </div>
                        {contact.last_contact && (
                          <div className="flex items-center space-x-1">
                            <ClockIcon className="h-3 w-3" />
                            <span>{formatDate(contact.last_contact)}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex flex-col items-end space-y-2">
                    {/* Opt-in status */}
                    <div className="flex items-center space-x-1">
                      {contact.opt_in_status ? (
                        <CheckCircleIcon className="h-4 w-4 text-green-500" />
                      ) : (
                        <XMarkIcon className="h-4 w-4 text-red-500" />
                      )}
                      <span className={`text-xs font-medium ${
                        contact.opt_in_status ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {contact.opt_in_status ? 'Opted In' : 'Opted Out'}
                      </span>
                    </div>
                    
                    {/* Email if available */}
                    {contact.email && (
                      <span className="text-xs text-gray-500 truncate max-w-24">
                        {contact.email}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
