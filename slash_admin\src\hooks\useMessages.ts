import { useState, useEffect } from 'react';
import { SMSMessage } from '../types';
import { exportCaseToPDF } from '../utils/pdfExport';
import messageService from '@/api/services/messageService';
import { toast } from 'sonner';

interface PaginatedMessages {
  count: number;
  next: string | null;
  previous: string | null;
  results: SMSMessage[];
}

export function useMessages() {
  const [messages, setMessages] = useState<SMSMessage[]>([]);
  const [messagesList, setMessagesList] = useState<any[]>([]);
  const [selectedContactMessages, setSelectedContactMessages] = useState<SMSMessage[]>([]);
  const [paginationInfo, setPaginationInfo] = useState({
    count: 0,
    next: null as string | null,
    previous: null as string | null,
    currentPage: 1,
    totalPages: 1
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);


  const fetchMessages = async (params?: {
    status?: string;
    category?: string;
    priority?: string;
    assigned_to?: string;
    search?: string;
    page?: number;
    page_size?: number;
    tags?: string;
    date_start?: string;
    date_end?: string;
    archived?: boolean;
    ordering?: string;
  }) => {
    // Prevent rapid successive calls (circuit breaker)
    const now = Date.now();
    if (now - lastFetchTime < 1000) { // Minimum 1 second between calls
      console.log('Skipping fetch - too soon after last call');
      return;
    }
    setLastFetchTime(now);

    try {
      setLoading(true);
      setError(null);

      console.log('Fetching messages with params:', params);
      const response = await messageService.getMessages(params);

      // Handle different response structures
      let messagesData, paginationData;

      if (response.results) {
        // Paginated response
        messagesData = response.results;
        paginationData = {
          count: response.count || 0,
          next: response.next,
          previous: response.previous,
          currentPage: params?.page || 1,
          totalPages: Math.ceil((response.count || 0) / (params?.page_size || 20))
        };
      } else if (Array.isArray(response)) {
        // Direct array response
        messagesData = response;
        paginationData = {
          count: response.length,
          next: null,
          previous: null,
          currentPage: 1,
          totalPages: 1
        };
      } else {
        throw new Error('Unexpected response format');
      }

      // Update pagination info
      setPaginationInfo(paginationData);

      // Normalize messages from API response
      const normalizedMessages = messagesData.map((msg: any) => ({
        id: msg.id,
        caseId: msg.case_id || `CASE-${msg.id}`,
        category: msg.category || 'general',
        content: msg.content || '',
        status: msg.status || 'new',
        priority: msg.priority || 'medium',
        phone_number:  msg.direction === 'inbound' ? msg.source_addr || '' : msg.dest_addr || '',
        tags: msg.tags || [],
        isArchived: msg.is_archived || false,
        timestamp: new Date(msg.timestamp || Date.now()),
        assigned_to: msg.assigned_to ? {
          id: msg.assigned_to.id,
          name: msg.assigned_to.name || msg.assigned_to.username || 'Unknown',
          email: msg.assigned_to.email || '',
          avatar: msg.assigned_to.avatar || null,
          role: msg.assigned_to.role || 'technician',
          department: msg.assigned_to.department || 'Operations',
          isActive: msg.assigned_to.is_active !== false
        } : undefined,
        attachments: msg.attachments || [],
        replyCount: msg.reply_count || 0,
        lastReply: msg.last_reply || null,
        replies: msg.replies || [],
      }));

      setMessages(normalizedMessages);
      console.log('Normalized messages:', normalizedMessages);
    } catch (err: any) {
      console.error('Error fetching messages:', err);
      const errorMessage = err.response?.data?.error || err.message || 'Failed to fetch messages';
      setError(errorMessage);

      // Don't clear messages on error, keep existing ones
      if (messages.length === 0) {
        setMessages([]);
      }
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch messages for a specific contact (for MessageDetail)
  const fetchMessagesForContact = async (sourceAddr: string) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fetching messages for contact:', sourceAddr);

      // Fetch all messages for this specific source_addr
      const response = await messageService.getMessages({
        search: sourceAddr, // Use search to filter by source_addr
        page_size: 100, // Get more messages for the conversation
        ordering: 'created_at' // Order by creation time for conversation flow
      } as any);

      console.log('Messages for contact response:', response);

      if (response && response.results) {
        // Convert to frontend format
        const convertedMessages = response.results.map((msg: any) => ({
          id: msg.id,
          caseId: msg.case_id || `CASE-${msg.id}`,
          category: msg.category || 'general',
          content: msg.content || '',
          status: msg.status || 'new',
          priority: msg.priority || 'medium',
          phone_number: msg.direction === 'inbound' ? msg.source_addr || '' : msg.dest_addr || '',
          timestamp: new Date(msg.created_at || Date.now()),
          tags: msg.tags || [],
          assigned_to: msg.assigned_to,
          replyCount: 0,
          lastReply: {} as any,
          replies: [],
          attachments: msg.attachments || [],
          isArchived: msg.archived || false,
          // Backend compatibility fields
          conversation: msg.conversation,
          direction: msg.direction,
          source_addr: msg.source_addr,
          dest_addr: msg.dest_addr,
          message_id: msg.message_id,
          case: msg.case
        }));

        setSelectedContactMessages(convertedMessages);
        return convertedMessages;
      } else {
        console.warn('No messages found for contact:', sourceAddr);
        setSelectedContactMessages([]);
        return [];
      }
    } catch (err: any) {
      console.error('Error fetching messages for contact:', err);
      setError(err.message || 'Failed to fetch contact messages');
      setSelectedContactMessages([]);
      return [];
    } finally {
      setLoading(false);
    }
  };

  // New function specifically for MessageList to get unique contacts
  const fetchMessageContacts = async (params?: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await messageService.getMessageContacts(params);
      console.log('useMessages - MessageContacts response:', response);

      if (response && response.results) {
        // Convert backend contact format to frontend SMSMessage format for compatibility
        const convertedContacts = response.results.map((contact: any) => ({
          id: contact.id, // This is the source_addr
          phone_number: contact.phone_number || contact.source_addr,
          content: contact.latest_message?.content || '',
          timestamp: new Date(contact.latest_message?.created_at || Date.now()),
          status: contact.status === 'new' ? 'new' as const : 'replied' as const,
          category: contact.category || 'general',
          priority: contact.priority || 'medium',
          tags: [],
          assigned_to: undefined,
          caseId: '',
          replyCount: contact.message_count || 0,
          lastReply: {} as any,
          replies: [],
          attachments: [],
          isArchived: false,
          // Backend compatibility fields
          conversation: contact.conversation_id,
          direction: contact.latest_message?.direction || 'inbound',
          source_addr: contact.source_addr,
          dest_addr: contact.source_addr, // For display purposes
          message_id: contact.latest_message?.id,
          case: null,
          // Additional fields for MessageList
          contact_name: contact.contact_name,
          message_count: contact.message_count,
          unread_count: contact.unread_count,
          latest_message: contact.latest_message
        }));

        console.log('useMessages - Setting converted message contacts:', convertedContacts);
        setMessagesList(convertedContacts); // Set messagesList for MessageList component

        setPaginationInfo({
          count: response.count || 0,
          next: response.next || null,
          previous: response.previous || null,
          currentPage: params?.page || 1,
          totalPages: Math.ceil((response.count || 0) / (params?.page_size || 20))
        });
      } else {
        console.warn('useMessages - No results in message contacts response:', response);
        setMessagesList([]);
      }
    } catch (err: any) {
      console.error('useMessages - Error fetching message contacts:', err);
      setError(err.message || 'Failed to fetch message contacts');
      setMessages([]);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch on mount - only once
  useEffect(() => {
    fetchMessages({
      page: 1,
      page_size: 20,
      archived: false,
      // ordering: '-timestamp'
    });
  }, []); // Empty dependency array - only run once

  const updateMessageStatus = (messageId: string, status: SMSMessage['status']) => {
    setMessages(prev => 
      prev.map(msg => 
        msg.id === messageId ? { ...msg, status } : msg
      )
    );
  };

  const getReplies = async (messageId: string) => {
    try {
      const response = await messageService.getReplies({ message_id: messageId });
      console.log('replies response:', response);

      // Convert backend messages to frontend Reply format
      const replies = response.results?.map((msg: any) => ({
        id: msg.id,
        content: msg.content,
        timestamp: new Date(msg.created_at),
        sender: msg.direction === 'outbound' ? {
          id: 'agent',
          name: 'Agent',
          email: '<EMAIL>',
          role: 'technician' as const,
          department: 'Support',
          isActive: true
        } : null,
        is_from_customer: msg.direction === 'inbound'? true : false,
        direction: msg.direction,
        attachments: [],
        status: msg.status
      })) || [];

      return replies;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch replies');
      console.error('Error fetching replies:', err);
      throw err;
    }
  };

  const addReply = async (messageId: string, content: string, sender: any) => {

    try {
      const reply = await messageService.addReply(messageId, content);;
      setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? {
              ...msg,
              replies: [
                ...msg.replies,
                {
                  id: `reply-${Date.now()}`,
                  content,
                  timestamp: new Date(),
                  sender,
                  is_from_customer: false,
                  direction: 'outbound' as const,
                  attachments: [],
                  status: 'sent' as const
                }
              ],
              status: 'replied' as const
            }
          : msg
      )
    );
      return reply;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to add reply');
      console.error('Error adding reply:', err);
      throw err;
    }
    
    
  };

  const assignMessage = (messageId: string, user: any) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, assigned_to: user, status: 'in-progress' as const }
          : msg
      )
    );
  };

  const addTag = (messageId: string, tag: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, tags: [...msg.tags, tag] }
          : msg
      )
    );
  };

  const archiveMessage = (messageId: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isArchived: true, archivedAt: new Date() }
          : msg
      )
    );
  };

  const restoreMessage = (messageId: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isArchived: false, archivedAt: undefined }
          : msg
      )
    );
  };

  const deleteMessage = (messageId: string) => {
    // setMessages(prev => prev.filter(msg => msg.id !== messageId));
    console.log('messageId', messageId);
    try {
      // Note: You might want to implement a soft delete in the backend
      messageService.deleteMessage(messageId);
      toast.success('Message deleted successfully');
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to delete message');
      console.error('Error deleting message:', err);
    }
  };

  const exportToPDF = (messageId: string) => {
    const message = messages.find(msg => msg.id === messageId);
    if (message) {
      exportCaseToPDF(message);
    }
  };

  // Backend-compatible functions
  const fetchConversations = async (params?: any) => {
    try {
      const response = await messageService.getConversations(params);
      return response;
    } catch (err: any) {
      console.error('Error fetching conversations:', err);
      throw err;
    }
  };

  const fetchCases = async (params?: any) => {
    try {
      const response = await messageService.getCases(params);
      return response;
    } catch (err: any) {
      console.error('Error fetching cases:', err);
      throw err;
    }
  };

  const fetchContacts = async (params?: any) => {
    try {
      const response = await messageService.getContacts(params);
      return response;
    } catch (err: any) {
      console.error('Error fetching contacts:', err);
      throw err;
    }
  };

  const sendMessage = async (data: {
    dest_addr: string;
    content: string;
    source_addr?: string;
    conversation_id?: string;
    priority?: string;
  }) => {
    try {
      const response = await messageService.sendMessage(data);
      toast.success('Message sent successfully');
      // Refresh messages
      await fetchMessages();
      return response;
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
      throw error;
    }
  };

  const sendBulkMessage = async (data: {
    recipients: string[];
    content: string;
    source_addr?: string;
    priority?: string;
  }) => {
    try {
      const response = await messageService.sendBulkMessage(data);
      toast.success(`Bulk message sent to ${data.recipients.length} recipients`);
      return response;
    } catch (error) {
      console.error('Error sending bulk message:', error);
      toast.error('Failed to send bulk message');
      throw error;
    }
  };

  return {
    messages,
    messagesList,
    selectedContactMessages,
    loading,
    error,
    paginationInfo,
    fetchMessages,
    fetchMessageContacts,  // New function for MessageList
    fetchMessagesForContact, // New function to fetch messages for specific contact
    updateMessageStatus,
    addReply,
    assignMessage,
    addTag,
    archiveMessage,
    restoreMessage,
    deleteMessage,
    exportToPDF,
    getReplies,
    // Backend-compatible functions
    fetchConversations,
    fetchCases,
    fetchContacts,
    sendMessage,
    sendBulkMessage,
    // Utility functions
    refetch: () => fetchMessages(),
    getMessageById: (id: string) => messages.find(msg => msg.id === id),
    getMessagesByStatus: (status: string) => messages.filter(msg => msg.status === status),
    getMessagesByCategory: (category: string) => messages.filter(msg => msg.category === category),
    getMessagesByPriority: (priority: string) => messages.filter(msg => msg.priority === priority),
    getUnreadCount: () => messages.filter(msg => msg.status === 'new').length,
    getHighPriorityCount: () => messages.filter(msg => msg.priority === 'high' || msg.priority === 'critical').length,
  };
}