export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'supervisor' | 'technician' | 'viewer';
  department: string;
  avatar?: string;
  isActive: boolean;
}

// Backend-compatible types
export interface Contact {
  id: string;
  phone_number: string;
  name: string;
  email?: string;
  company?: string;
  location?: string;
  preferred_language: string;
  opt_in_status: boolean;
  opt_out_date?: string;
  ton: number;
  npi: number;
  message_count: number;
  case_count: number;
  last_contact?: string;
  created_at: string;
  updated_at: string;
}

export interface Conversation {
  id: string;
  contact: Contact;
  // Backend compatibility fields
  contact_name?: string;
  contact_phone_number?: string;
  smsc_number?: {
    id: string;
    number: string;
    description: string;
  };
  subject?: string;
  status: 'active' | 'pending' | 'resolved' | 'closed';
  priority: number;
  assigned_to?: User;
  assigned_at?: string;
  message_count: number;
  unread_count: number;
  last_message_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Message {
  id: string;
  conversation: Conversation;
  direction: 'inbound' | 'outbound';
  content: string;
  source_addr: string;
  dest_addr: string;
  status: 'enroute' | 'delivered' | 'undeliverable' | 'unknown';
  smpp_connection?: {
    id: string;
    name: string;
    host: string;
    port: number;
  };
  case?: {
    id: string;
    case_id: string;
    title: string;
    status: string;
  };
  data_coding: number;
  source_addr_ton: number;
  source_addr_npi: number;
  dest_addr_ton: number;
  dest_addr_npi: number;
  message_id?: string;
  submitted_at?: string;
  delivered_at?: string;
  created_at: string;
  // Note: Message model doesn't have updated_at field, using created_at for compatibility
}

export interface SMSMessage {
  id: string;
  phone_number: string;
  content: string;
  timestamp: Date;
  status: 'new' | 'in-progress' | 'replied' | 'closed';
  category: 'power-outage' | 'wire-cut' | 'fallen-pole' | 'corruption' | 'billing' | 'general' | 'other';
  priority: 'low' | 'medium' | 'high' | 'critical';
  tags: string[];
  assigned_to?: User;
  caseId: string;
  replyCount: number;
  lastReply: Reply;
  replies: Reply[];
  attachments: Attachment[];
  isArchived: boolean;
  archivedAt?: Date;
  archivedBy?: User;
  // Backend compatibility fields
  conversation?: Conversation;
  direction?: 'inbound' | 'outbound';
  source_addr?: string;
  dest_addr?: string;
  message_id?: string;
  case?: any;
}

export interface Reply {
  id: string;
  content: string;
  timestamp: Date;
  sender: User | null;
  is_from_customer: boolean;
  direction: 'inbound' | 'outbound';
  attachments: Attachment[];
  status?: 'pending' | 'sent' | 'delivered' | 'failed';
}

export interface Attachment {
  id: string;
  name: string;
  url: string;
  type: 'image' | 'document' | 'other';
  size: number;
  uploadedAt: Date;
  uploadedBy: User;
}

export interface Case {
  id: string;
  case_id: string;
  conversation: Conversation;
  origin_message?: Message;
  title: string;
  description: string;
  status: 'new' | 'open' | 'in-progress' | 'resolved' | 'closed';
  category: 'power-outage' | 'bribery'  | 'emergency' | 'billing' | 'general';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  tags: string[];
  assigned_to?: User;
  assigned_at?: string;
  sla_due_at?: string;
  sla_violated: boolean;
  resolution_notes?: string;
  created_at: string;
  updated_at: string;
  // Legacy compatibility
  phone_number?: string;
  createdAt?: Date;
  updatedAt?: Date;
  messages?: SMSMessage[];
  notes?: string;
  timeline?: TimelineEvent[];
  assignmentHistory?: AssignmentHistory[];
  dueDate?: Date;
  taskComment?: string;
  checklist?: ChecklistItem[];
  reports?: ProgressReport[];
}

export interface TimelineEvent {
  id: string;
  type: 'message' | 'assignment' | 'status_change' | 'reply' | 'report' | 'note';
  timestamp: Date;
  user: User;
  description: string;
  metadata?: any;
}

export interface AssignmentHistory {
  id: string;
  fromUser?: User;
  toUser: User;
  timestamp: Date;
  reason?: string;
  assignedBy: User;
}

export interface ChecklistItem {
  id: string;
  text: string;
  completed: boolean;
  completedAt?: Date;
  completedBy?: User;
}

export interface ProgressReport {
  id: string;
  content: string;
  timestamp: Date;
  reportedBy: User;
  attachments: Attachment[];
  checklist: ChecklistItem[];
}

export interface Notification {
  id: string;
  type: 'assignment' | 'message' | 'report' | 'status_change';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  userId: string;
  caseId?: string;
  messageId?: string;
}

export interface Department {
  id: string;
  name: string;
  color: string;
}

export interface SystemSettings {
  categories: CategoryConfig[];
  workingHours: WorkingHours;
  autoReplies: AutoReplyConfig[];
  notifications: NotificationSettings;
}

export interface CategoryConfig {
  id: string;
  name: string;
  color: string;
  autoAssignDepartment?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  keywords: string[];
}

export interface WorkingHours {
  monday: { start: string; end: string; enabled: boolean };
  tuesday: { start: string; end: string; enabled: boolean };
  wednesday: { start: string; end: string; enabled: boolean };
  thursday: { start: string; end: string; enabled: boolean };
  friday: { start: string; end: string; enabled: boolean };
  saturday: { start: string; end: string; enabled: boolean };
  sunday: { start: string; end: string; enabled: boolean };
}

export interface AutoReplyConfig {
  id: string;
  category: string;
  template: string;
  enabled: boolean;
  conditions: string[];
}

export interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  assignmentNotifications: boolean;
  reportNotifications: boolean;
}
export interface Analytics {
  totalCases: number;
  resolvedCases: number;
  pendingCases: number;
  averageResponseTime: number;
  categoryCounts: Record<string, number>;
  statusCounts: Record<string, number>;
  dailyTrends: Array<{
    date: string;
    cases: number;
    resolved: number;
  }>;
}

export interface MessageTemplate {
  id: string;
  name: string;
  category: string;
  content: string;
  variables: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface DetailedReport {
  id: string;
  messageId: string;
  caseId: string;
  title: string;
  summary: string;
  detailedDescription: string;
  actionsTaken: string;
  outcome: string;
  recommendations: string;
  followUpRequired: boolean;
  followUpDate: string;
  followUpNotes: string;
  checklist: ChecklistItem[];
  attachments: Attachment[];
  createdAt: Date;
  createdBy: User;
}

export interface ApprovalRequest {
  id: string;
  messageId: string;
  type: 'budget_approval' | 'escalation' | 'resource_request' | 'policy_exception';
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  requestedBy: User;
  requestedAt: Date;
  approvers: User[];
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: User;
  approvedAt?: Date;
  comments?: string;
  approvalHistory: ApprovalHistory[];
}

export interface ApprovalHistory {
  id: string;
  decision: 'approved' | 'rejected';
  comments: string;
  approvedBy: User;
  approvedAt: Date;
}

export interface AssignmentStatus {
  id: string;
  messageId: string;
  assigned_to: User;
  assignedBy: User;
  assignedAt: Date;
  priority: 'low' | 'medium' | 'high' | 'critical';
  dueDate: Date;
  estimatedHours: number;
  skillsRequired: string[];
  status: 'assigned' | 'accepted' | 'in_progress' | 'completed' | 'rejected';
  acceptedAt?: Date;
  completedAt?: Date;
  comments: string;
  progressUpdates: ProgressUpdate[];
}

export interface ProgressUpdate {
  id: string;
  content: string;
  timestamp: Date;
  updatedBy: User;
  percentComplete: number;
}