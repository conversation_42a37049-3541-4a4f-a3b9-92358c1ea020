import React from 'react';
import TemplateManager from './TemplateManager';
// import { useTemplates } from '../hooks/useTemplates';
// import { useAuth } from '../contexts/AuthContext';

export default function Templates() {
  // const { currentUser } = useAuth();
  // const { templates, saveTemplate, deleteTemplate } = useTemplates();

  const currentUser = {
    role: 'admin'
  };

  const saveTemplate = (template: any) => {
    // Implement save logic here
  };

  const deleteTemplate = (templateId: string) => {
    // Implement delete logic here
  };

  const templates: any[] = [
  {
    id: '1',
    name: 'Power Outage Response',
    category: 'power-outage',
    content: 'Thank you for reporting the power outage at {{address}}. Our crew has been dispatched and we estimate restoration within {{hours}} hours. We will keep you updated on progress.',
    variables: ['address', 'hours'],
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '2',
    name: 'Billing Inquiry Response',
    category: 'billing',
    content: 'Thank you for your billing inquiry regarding account {{account_number}}. I will review your account and respond within {{timeframe}} with detailed information.',
    variables: ['account_number', 'timeframe'],
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '3',
    name: 'Emergency Wire Down',
    category: 'wire-cut',
    content: 'URGENT: Thank you for reporting the downed wire at {{location}}. Please stay away from the area for safety. A crew is en route and will secure the area within {{eta}} minutes.',
    variables: ['location', 'eta'],
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '4',
    name: 'General Acknowledgment',
    category: 'general',
    content: 'Thank you for contacting EEU. Your message has been received and assigned case number {{case_id}}. We will respond within {{response_time}}.',
    variables: ['case_id', 'response_time'],
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  }
];

  if (currentUser?.role !== 'admin' && currentUser?.role !== 'supervisor') {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="text-red-800">Access Denied</div>
          <div className="text-red-600 text-sm">You need admin or supervisor privileges to manage templates.</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Message Templates</h1>
        <p className="text-gray-600">Manage dynamic response templates for different categories</p>
      </div>
      
      <TemplateManager
        templates={templates}
        onSave={saveTemplate}
        onDelete={deleteTemplate}
      />
    </div>
  );
}