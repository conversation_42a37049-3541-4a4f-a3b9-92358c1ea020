import React, { useState } from 'react';
import { 
  Card, 
  Descriptions, 
  Tag, 
  Button, 
  Space, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message,
  Tabs,
  Timeline,
  Typography
} from 'antd';
import { 
  EditOutlined, 
  SaveOutlined, 
  CloseOutlined,
  ClockCircleOutlined,
  UserOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { Case } from '../../types';
import dayjs from 'dayjs';
import messageService from '../../api/services/messageService';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

interface CaseDetailRealProps {
  case: Case;
  onBack: () => void;
  onCaseUpdate: (updatedCase: Case) => void;
}

const CaseDetailReal: React.FC<CaseDetailRealProps> = ({ case: caseData, onBack, onCaseUpdate }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const getStatusColor = (status: string) => {
    const colors = {
      new: 'blue',
      open: 'green',
      'in-progress': 'orange',
      resolved: 'purple',
      closed: 'gray',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: 'green',
      medium: 'yellow',
      high: 'orange',
      urgent: 'red',
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  const handleEdit = () => {
    setIsEditing(true);
    form.setFieldsValue({
      title: caseData.title,
      description: caseData.description,
      status: caseData.status,
      priority: caseData.priority,
      category: caseData.category,
    });
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      const updatedCase = await messageService.updateCase(caseData.id, values);
      
      onCaseUpdate({ ...caseData, ...values, updated_at: new Date().toISOString() });
      setIsEditing(false);
      message.success('Case updated successfully');
    } catch (error) {
      console.error('Error updating case:', error);
      message.error('Failed to update case');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    form.resetFields();
  };

  const OverviewTab = () => (
    <div className="space-y-6">
      <Card title="Case Information" className="shadow-sm">
        <Descriptions column={2} bordered>
          <Descriptions.Item label="Case ID">
            <span className="font-mono">{caseData.case_id}</span>
          </Descriptions.Item>
          <Descriptions.Item label="Status">
            <Tag color={getStatusColor(caseData.status)}>
              {caseData.status.toUpperCase()}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Priority">
            <Tag color={getPriorityColor(caseData.priority)}>
              {caseData.priority.toUpperCase()}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Category">
            <Tag>{caseData.category.toUpperCase()}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Created">
            {dayjs(caseData.created_at).format('MMM DD, YYYY HH:mm')}
          </Descriptions.Item>
          <Descriptions.Item label="Updated">
            {dayjs(caseData.updated_at).format('MMM DD, YYYY HH:mm')}
          </Descriptions.Item>
          <Descriptions.Item label="Assigned To">
            {caseData.assigned_to?.name || 'Unassigned'}
          </Descriptions.Item>
          <Descriptions.Item label="SLA Status">
            {caseData.sla_violated ? (
              <Tag color="red">SLA Violated</Tag>
            ) : (
              <Tag color="green">Within SLA</Tag>
            )}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card title="Description" className="shadow-sm">
        {isEditing ? (
          <Form form={form} layout="vertical">
            <Form.Item name="title" label="Title" rules={[{ required: true }]}>
              <Input />
            </Form.Item>
            <Form.Item name="description" label="Description" rules={[{ required: true }]}>
              <TextArea rows={4} />
            </Form.Item>
            <Form.Item name="status" label="Status">
              <Select>
                <Option value="new">New</Option>
                <Option value="open">Open</Option>
                <Option value="in-progress">In Progress</Option>
                <Option value="resolved">Resolved</Option>
                <Option value="closed">Closed</Option>
              </Select>
            </Form.Item>
            <Form.Item name="priority" label="Priority">
              <Select>
                <Option value="low">Low</Option>
                <Option value="medium">Medium</Option>
                <Option value="high">High</Option>
                <Option value="urgent">Urgent</Option>
              </Select>
            </Form.Item>
            <Form.Item name="category" label="Category">
              <Select>
                <Option value="power-outage">Power Outage</Option>
                <Option value="bribery">Bribery</Option>
                {/* <Option value="telecom">Telecom</Option> */}
                <Option value="emergency">Emergency</Option>
                <Option value="billing">Billing</Option>
                <Option value="general">General</Option>
              </Select>
            </Form.Item>
          </Form>
        ) : (
          <div>
            <Title level={4}>{caseData.title}</Title>
            <Paragraph>{caseData.description}</Paragraph>
          </div>
        )}
      </Card>

      {caseData.tags && caseData.tags.length > 0 && (
        <Card title="Tags" className="shadow-sm">
          <Space wrap>
            {caseData.tags.map((tag, index) => (
              <Tag key={index} color="blue">{tag}</Tag>
            ))}
          </Space>
        </Card>
      )}

      {caseData.conversation && (
        <Card title="Related Conversation" className="shadow-sm">
          <Descriptions column={1} bordered>
            <Descriptions.Item label="Contact">
              {caseData.conversation.contact_name || 'Unknown'}
            </Descriptions.Item>
            <Descriptions.Item label="Phone">
              {caseData.conversation.contact_phone_number || 'Unknown'}
            </Descriptions.Item>
          </Descriptions>
        </Card>
      )}
    </div>
  );

  const TimelineTab = () => (
    <Card title="Case Timeline" className="shadow-sm">
      <Timeline>
        <Timeline.Item 
          dot={<ClockCircleOutlined />}
          color="blue"
        >
          <div>
            <strong>Case Created</strong>
            <div className="text-gray-500 text-sm">
              {dayjs(caseData.created_at).format('MMM DD, YYYY HH:mm')}
            </div>
          </div>
        </Timeline.Item>
        
        {caseData.assigned_at && (
          <Timeline.Item 
            dot={<UserOutlined />}
            color="green"
          >
            <div>
              <strong>Case Assigned</strong>
              <div className="text-gray-500 text-sm">
                Assigned to {caseData.assigned_to?.name || 'Unknown'}
              </div>
              <div className="text-gray-500 text-sm">
                {dayjs(caseData.assigned_at).format('MMM DD, YYYY HH:mm')}
              </div>
            </div>
          </Timeline.Item>
        )}
        
        <Timeline.Item 
          dot={<FileTextOutlined />}
          color="gray"
        >
          <div>
            <strong>Last Updated</strong>
            <div className="text-gray-500 text-sm">
              {dayjs(caseData.updated_at).format('MMM DD, YYYY HH:mm')}
            </div>
          </div>
        </Timeline.Item>
      </Timeline>
    </Card>
  );

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button onClick={onBack}>← Back</Button>
            <div>
              <Title level={3} className="mb-0">{caseData.case_id}</Title>
              <p className="text-gray-600 mb-0">{caseData.title}</p>
            </div>
          </div>
          <Space>
            {isEditing ? (
              <>
                <Button onClick={handleCancel} icon={<CloseOutlined />}>
                  Cancel
                </Button>
                <Button 
                  type="primary" 
                  onClick={handleSave} 
                  loading={loading}
                  icon={<SaveOutlined />}
                >
                  Save
                </Button>
              </>
            ) : (
              <Button 
                type="primary" 
                onClick={handleEdit}
                icon={<EditOutlined />}
              >
                Edit Case
              </Button>
            )}
          </Space>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-6">
        <Tabs defaultActiveKey="overview" className="h-full">
          <TabPane tab="Overview" key="overview">
            <OverviewTab />
          </TabPane>
          <TabPane tab="Timeline" key="timeline">
            <TimelineTab />
          </TabPane>
        </Tabs>
      </div>
    </div>
  );
};

export default CaseDetailReal;
