import React, { useEffect, useState } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  Card,
  Row,
  Col,
  Tooltip,
  message as antMessage,
  Popconfirm,
  Drawer,
  DatePicker,
  Collapse,
  Divider
} from 'antd';
import type { ColumnsType, TableProps } from 'antd/es/table';
import type { FilterValue, SorterResult } from 'antd/es/table/interface';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExportOutlined,
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  FilterOutlined,
  DownOutlined
} from '@ant-design/icons';
import * as XLSX from 'xlsx';
import { toast } from 'sonner';
import dayjs from 'dayjs';

import CaseDetailReal from './CaseDetailReal';
import messageService from '../../api/services/messageService';
import { Case } from '@/types';


const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Panel } = Collapse;

interface TableParams {
  pagination?: {
    current?: number;
    pageSize?: number;
    total?: number;
  };
  sortField?: string;
  sortOrder?: string;
  filters?: Record<string, FilterValue | null>;
}

export default function CaseManagement() {
  const [cases, setCases] = useState<Case[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedCase, setSelectedCase] = useState<Case | null>(null);
  const [showDetail, setShowDetail] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingCase, setEditingCase] = useState<Case | null>(null);
  const [searchText, setSearchText] = useState('');
  const [filteredInfo, setFilteredInfo] = useState<Record<string, FilterValue | null>>({});
  const [sortedInfo, setSortedInfo] = useState<SorterResult<Case>>({});

  // Advanced filter state
  const [showFilters, setShowFilters] = useState(false);
  const [advancedFilters, setAdvancedFilters] = useState({
    status: undefined as string | undefined,
    priority: undefined as string | undefined,
    category: undefined as string | undefined,
    dateRange: undefined as [dayjs.Dayjs, dayjs.Dayjs] | undefined,
  });

  const [tableParams, setTableParams] = useState<TableParams>({
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
  });

  const [form] = Form.useForm();

  // Fetch cases data
  const fetchCases = async (params?: any) => {
    setLoading(true);
    try {
      const filterParams: any = {
        page: params?.pagination?.current || 1,
        page_size: params?.pagination?.pageSize || 20,
        search: searchText,
        ordering: params?.sortField ? `${params?.sortOrder === 'ascend' ? '' : '-'}${params?.sortField}` : '-created_at'
      };

      // Add advanced filters
      if (advancedFilters.status) {
        filterParams.status = advancedFilters.status;
      }
      if (advancedFilters.priority) {
        filterParams.priority = advancedFilters.priority;
      }
      if (advancedFilters.category) {
        filterParams.category = advancedFilters.category;
      }
      if (advancedFilters.dateRange) {
        filterParams.created_after = advancedFilters.dateRange[0].format('YYYY-MM-DD');
        filterParams.created_before = advancedFilters.dateRange[1].format('YYYY-MM-DD');
      }

      // Add table filters
      if (params?.filters) {
        Object.keys(params.filters).forEach(key => {
          if (params.filters[key]) {
            filterParams[key] = params.filters[key];
          }
        });
      }

      const response = await messageService.getCases(filterParams);

      setCases(response.results || []);
      setTableParams({
        pagination: {
          current: params?.pagination?.current || 1,
          pageSize: params?.pagination?.pageSize || 20,
          total: response.count || 0,
        },
      });
    } catch (error) {
      console.error('Error fetching cases:', error);
      toast.error('Failed to fetch cases');
    } finally {
      setLoading(false);
    }
  };

  // Fetch cases when component mounts
  useEffect(() => {
    fetchCases(tableParams);
  }, []);

  // Fetch cases when search text changes
  useEffect(() => {
    // Reset to first page when search text changes
    const newTableParams = {
      ...tableParams,
      pagination: {
        ...tableParams.pagination,
        current: 1,
      },
    };
    setTableParams(newTableParams);
    fetchCases(newTableParams);
  }, [searchText]);

  // Action handlers
  const handleTableChange: TableProps<Case>['onChange'] = (pagination, filters, sorter) => {
    const params = {
      pagination,
      filters,
      sortField: Array.isArray(sorter) ? undefined : sorter.field as string,
      sortOrder: Array.isArray(sorter) ? undefined : sorter.order as string,
    };

    setTableParams(params);
    setFilteredInfo(filters || {});
    setSortedInfo(Array.isArray(sorter) ? {} : sorter);
    fetchCases(params);
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  const handleRefresh = () => {
    fetchCases(tableParams);
  };

  const handleCreate = () => {
    setEditingCase(null);
    form.resetFields();
    setShowCreateModal(true);
  };

  const handleEdit = (record: Case) => {
    setEditingCase(record);
    form.setFieldsValue({
      ...record,
      created_at: record.created_at ? dayjs(record.created_at) : undefined,
      updated_at: record.updated_at ? dayjs(record.updated_at) : undefined,
    });
    setShowCreateModal(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await messageService.deleteCase(id);
      antMessage.success('Case deleted successfully');
      fetchCases(tableParams);
    } catch (error) {
      console.error('Error deleting case:', error);
      antMessage.error('Failed to delete case');
    }
  };

  const handleView = (record: Case) => {
    setSelectedCase(record);
    setShowDetail(true);
  };

  const handleExport = async () => {
    try {
      // Show loading indicator
      antMessage.loading({ content: 'Preparing export...', key: 'export', duration: 0 });

      // Build export parameters with current filters and search
      const exportParams: any = {
        // format: 'xlsx', // Tell server we want Excel export
        page_size: 'all', // Get all records for export
        search: searchText,
        // Include current table sorting
        ordering: sortedInfo.field ? `${sortedInfo.order === 'ascend' ? '' : '-'}${sortedInfo.field}` : '-created_at'
      };

      // Add advanced filters to export params
      if (advancedFilters.status) {
        exportParams.status = advancedFilters.status;
      }
      if (advancedFilters.priority) {
        exportParams.priority = advancedFilters.priority;
      }
      if (advancedFilters.category) {
        exportParams.category = advancedFilters.category;
      }
      if (advancedFilters.dateRange) {
        exportParams.created_after = advancedFilters.dateRange[0].format('YYYY-MM-DD');
        exportParams.created_before = advancedFilters.dateRange[1].format('YYYY-MM-DD');
      }

      // Add table column filters
      if (filteredInfo) {
        Object.keys(filteredInfo).forEach(key => {
          if (filteredInfo[key] && filteredInfo[key]!.length > 0) {
            exportParams[key] = Array.isArray(filteredInfo[key])
              ? (filteredInfo[key] as string[]).join(',')
              : filteredInfo[key];
          }
        });
      }

      // Call server-side export API using the correct endpoint
      const response = await messageService.exportCases(exportParams);

      // Create filename with timestamp
      const timestamp = dayjs().format('YYYY-MM-DD_HH-mm-ss');
      const filename = `cases_export_${timestamp}.xlsx`;

      // Handle the response - it should already be a blob from the server
      let blob: Blob;
      if (response instanceof Blob) {
        blob = response;
      } else if (response instanceof ArrayBuffer) {
        blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
      } else {
        // If response is raw data, convert to blob
        blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
      }

      // Create download link and trigger download
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      antMessage.success({
        content: `Cases exported successfully! Download started for ${filename}`,
        key: 'export'
      });

    } catch (error) {
      console.error('Export error:', error);
      antMessage.error({
        content: `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        key: 'export'
      });
    }
  };

  const handleFormSubmit = async (values: any) => {
    try {
      if (editingCase) {
        console.log("Valuesssssssssssssssssss",values);
        await messageService.updateCase(editingCase.id, values);
        antMessage.success('Case updated successfully');
      } else {
        await messageService.createCase(values);
        antMessage.success('Case created successfully');
      }
      setShowCreateModal(false);
      fetchCases(tableParams);
    } catch (error) {
      console.error('Error saving case:', error);
      antMessage.error('Failed to save case');
    }
  };

  // Advanced filter handlers
  const handleAdvancedFilterChange = (key: string, value: any) => {
    setAdvancedFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleApplyFilters = () => {
    fetchCases(tableParams);
  };

  const handleClearFilters = () => {
    setAdvancedFilters({
      status: undefined,
      priority: undefined,
      category: undefined,
      dateRange: undefined,
    });
    setSearchText('');
    fetchCases(tableParams);
  };

  // Table columns
  const columns: ColumnsType<Case> = [
    {
      title: 'Case ID',
      dataIndex: 'case_id',
      key: 'case_id',
      width: 120,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'case_id' ? sortedInfo.order : null,
      render: (text: string) => (
        <span className="font-mono text-sm">{text}</span>
      ),
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'title' ? sortedInfo.order : null,
      render: (text: string, record: Case) => (
        <div>
          <div className="font-medium text-gray-900 truncate max-w-xs" title={text}>
            {text}
          </div>
          {record.description && (
            <div className="text-xs text-gray-500 truncate max-w-xs" title={record.description}>
              {record.description}
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      filters: [
        { text: 'New', value: 'new' },
        { text: 'Open', value: 'open' },
        { text: 'In Progress', value: 'in_progress' },
        { text: 'Resolved', value: 'resolved' },
        { text: 'Closed', value: 'closed' },
      ],
      filteredValue: filteredInfo.status || null,
      render: (status: string) => {
        const colors = {
          new: 'blue',
          open: 'green',
          in_progress: 'orange',
          resolved: 'purple',
          closed: 'gray',
        };
        return (
          <Tag color={colors[status as keyof typeof colors] || 'default'}>
            {status?.toUpperCase()}
          </Tag>
        );
      },
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      filters: [
        { text: 'Low', value: 'low' },
        { text: 'Medium', value: 'medium' },
        { text: 'High', value: 'high' },
        { text: 'Critical', value: 'critical' },
      ],
      filteredValue: filteredInfo.priority || null,
      render: (priority: string) => {
        const colors = {
          low: 'green',
          medium: 'yellow',
          high: 'orange',
          critical: 'red',
        };
        return (
          <Tag color={colors[priority as keyof typeof colors] || 'default'}>
            {priority?.toUpperCase()}
          </Tag>
        );
      },
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      filters: [
        { text: 'General', value: 'general' },
        { text: 'Technical', value: 'technical' },
        { text: 'Billing', value: 'billing' },
        { text: 'Complaint', value: 'complaint' },
        { text: 'Emergency', value: 'emergency' },
      ],
      filteredValue: filteredInfo.category || null,
      render: (category: string) => (
        <Tag>{category?.toUpperCase()}</Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      sorter: true,
      sortOrder: sortedInfo.columnKey === 'created_at' ? sortedInfo.order : null,
      render: (date: string) => (
        <span className="text-sm">
          {date ? dayjs(date).format('MMM DD, YYYY') : '-'}
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record: Case) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this case?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <Card className="mb-4">
        <Row justify="space-between" align="middle">
          <Col>
            <div className="flex items-center space-x-3">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Case Management</h1>
                <p className="text-sm text-gray-500">Manage and track support cases</p>
              </div>
            </div>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="Search cases..."
                allowClear
                onSearch={handleSearch}
                style={{ width: 250 }}
                enterButton={<SearchOutlined />}
              />
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
              >
                Refresh
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={handleExport}
              >
                Export
              </Button>
              <Button
                icon={<FilterOutlined />}
                onClick={() => setShowFilters(!showFilters)}
              >
                {showFilters ? 'Hide Filters' : 'Show Filters'}
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Advanced Filters */}
      {showFilters && (
        <Card className="mb-4">
          <Collapse
            defaultActiveKey={['1']}
            ghost
            expandIcon={({ isActive }) => <DownOutlined rotate={isActive ? 180 : 0} />}
          >
            <Panel header="Advanced Filters" key="1">
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={6}>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <Select
                      placeholder="Select status"
                      allowClear
                      style={{ width: '100%' }}
                      value={advancedFilters.status}
                      onChange={(value) => handleAdvancedFilterChange('status', value)}
                    >
                      <Option value="new">New</Option>
                      <Option value="open">Open</Option>
                      <Option value="in_progress">In Progress</Option>
                      <Option value="resolved">Resolved</Option>
                      <Option value="closed">Closed</Option>
                    </Select>
                  </div>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                    <Select
                      placeholder="Select priority"
                      allowClear
                      style={{ width: '100%' }}
                      value={advancedFilters.priority}
                      onChange={(value) => handleAdvancedFilterChange('priority', value)}
                    >
                      <Option value="low">Low</Option>
                      <Option value="medium">Medium</Option>
                      <Option value="high">High</Option>
                      <Option value="critical">Critical</Option>
                    </Select>
                  </div>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <Select
                      placeholder="Select category"
                      allowClear
                      style={{ width: '100%' }}
                      value={advancedFilters.category}
                      onChange={(value) => handleAdvancedFilterChange('category', value)}
                    >
                      <Option value="general">General</Option>
                      <Option value="technical">Technical</Option>
                      <Option value="billing">Billing</Option>
                      <Option value="complaint">Complaint</Option>
                      <Option value="emergency">Emergency</Option>
                    </Select>
                  </div>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                    <RangePicker
                      style={{ width: '100%' }}
                      value={advancedFilters.dateRange}
                      onChange={(dates) => handleAdvancedFilterChange('dateRange', dates)}
                      format="YYYY-MM-DD"
                    />
                  </div>
                </Col>
              </Row>
              <Divider />
              <Row justify="end">
                <Col>
                  <Space>
                    <Button onClick={handleClearFilters}>
                      Clear All
                    </Button>
                    <Button type="primary" onClick={handleApplyFilters}>
                      Apply Filters
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Panel>
          </Collapse>
        </Card>
      )}

      {/* Table */}
      <Card className="flex-1">
        <Table<Case>
          columns={columns}
          dataSource={cases}
          rowKey="id"
          loading={loading}
          pagination={{
            ...tableParams.pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} cases`,
          }}
          onChange={handleTableChange}
          scroll={{ y: 'calc(100vh - 250px)' }}
          size="small"
        />
      </Card>

      {/* Create/Edit Modal */}
      <Modal
        title={editingCase ? 'Edit Case' : 'Create New Case'}
        open={showCreateModal}
        onCancel={() => setShowCreateModal(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
          initialValues={{
            status: 'new',
            priority: 'medium',
            category: 'general',
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="title"
                label="Title"
                rules={[{ required: true, message: 'Please enter case title' }]}
              >
                <Input placeholder="Enter case title" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="case_id"
                label="Case ID"
                rules={[{ required: true, message: 'Please enter case ID' }]}
              >
                <Input placeholder="Enter case ID" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <Input.TextArea rows={4} placeholder="Enter case description" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="status" label="Status">
                <Select>
                  <Option value="new">New</Option>
                  <Option value="open">Open</Option>
                  <Option value="in_progress">In Progress</Option>
                  <Option value="resolved">Resolved</Option>
                  <Option value="closed">Closed</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="priority" label="Priority">
                <Select>
                  <Option value="low">Low</Option>
                  <Option value="medium">Medium</Option>
                  <Option value="high">High</Option>
                  <Option value="critical">Critical</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="category" label="Category">
                <Select>
                  <Option value="general">General</Option>
                  <Option value="technical">Technical</Option>
                  <Option value="billing">Billing</Option>
                  <Option value="complaint">Complaint</Option>
                  <Option value="emergency">Emergency</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="internal_notes" label="Internal Notes">
            <Input.TextArea rows={3} placeholder="Internal notes (optional)" />
          </Form.Item>

          <Form.Item className="mb-0">
            <Space className="w-full justify-end">
              <Button onClick={() => setShowCreateModal(false)}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {editingCase ? 'Update Case' : 'Create Case'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Case Detail Drawer */}
      <Drawer
        title="Case Details"
        placement="right"
        size="large"
        open={showDetail}
        onClose={() => setShowDetail(false)}
        width={800}
      >
        {selectedCase && (
          <CaseDetailReal
            case={selectedCase}
            onBack={() => setShowDetail(false)}
            onCaseUpdate={(updatedCase: Case) => {
              // Update the case in the list
              setCases(prev => prev.map(c => c.id === updatedCase.id ? updatedCase : c));
              setSelectedCase(updatedCase);
            }}
          />
        )}
      </Drawer>
    </div>
  );
}