/**
 * React Hook for WebSocket Integration
 * Provides easy WebSocket functionality for React components
 */

import { useEffect, useState, useCallback, useRef } from 'react';
import websocketService, { MessageData, ConversationUpdateData, ConversationListUpdateData } from '../services/websocketService';

export interface UseWebSocketOptions {
  conversationId?: string;
  onNewMessage?: (message: MessageData) => void;
  onMessageUpdated?: (message: MessageData) => void;
  onConversationUpdated?: (update: ConversationUpdateData) => void;
  onConversationListUpdated?: (update: ConversationListUpdateData) => void;
  onTypingStart?: (data: { conversation_id: string; user_id?: string }) => void;
  onTypingStop?: (data: { conversation_id: string; user_id?: string }) => void;
  onConnectionChange?: (connected: boolean) => void;
}

export interface UseWebSocketReturn {
  isConnected: boolean;
  sendMessage: (content: string, destAddr: string) => void;
  sendTyping: (isTyping: boolean) => void;
  subscribeToConversation: (conversationId: string) => void;
  unsubscribeFromConversation: (conversationId: string) => void;
  reconnect: () => void;
}

export const useWebSocket = (options: UseWebSocketOptions = {}): UseWebSocketReturn => {
  const [isConnected, setIsConnected] = useState(websocketService.isConnected());
  const optionsRef = useRef(options);
  const currentConversationId = useRef<string | null>(null);

  // Update options ref when options change
  useEffect(() => {
    optionsRef.current = options;
  }, [options]);

  // Handle new messages
  const handleNewMessage = useCallback((message: MessageData) => {
    console.log('🆕 New message received:', message);
    if (optionsRef.current.onNewMessage) {
      optionsRef.current.onNewMessage(message);
    }
  }, []);

  // Handle message updates
  const handleMessageUpdated = useCallback((message: MessageData) => {
    console.log('🔄 Message updated:', message);
    if (optionsRef.current.onMessageUpdated) {
      optionsRef.current.onMessageUpdated(message);
    }
  }, []);

  // Handle conversation updates
  const handleConversationUpdated = useCallback((update: ConversationUpdateData) => {
    console.log('🔄 Conversation updated:', update);
    if (optionsRef.current.onConversationUpdated) {
      optionsRef.current.onConversationUpdated(update);
    }
  }, []);

  // Handle conversation list updates
  const handleConversationListUpdated = useCallback((update: ConversationListUpdateData) => {
    console.log('📋 Conversation list updated:', update);
    if (optionsRef.current.onConversationListUpdated) {
      optionsRef.current.onConversationListUpdated(update);
    }
  }, []);

  // Handle typing start
  const handleTypingStart = useCallback((data: any) => {
    console.log('⌨️ Typing started:', data);
    if (optionsRef.current.onTypingStart) {
      optionsRef.current.onTypingStart(data);
    }
  }, []);

  // Handle typing stop
  const handleTypingStop = useCallback((data: any) => {
    console.log('⌨️ Typing stopped:', data);
    if (optionsRef.current.onTypingStop) {
      optionsRef.current.onTypingStop(data);
    }
  }, []);

  // Handle connection status changes
  const handleConnectionChange = useCallback((connected: boolean) => {
    console.log('🔌 Connection status changed:', connected);
    setIsConnected(connected);
    if (optionsRef.current.onConnectionChange) {
      optionsRef.current.onConnectionChange(connected);
    }
  }, []);

  // Subscribe to conversation
  const subscribeToConversation = useCallback((conversationId: string) => {
    if (currentConversationId.current && currentConversationId.current !== conversationId) {
      // Unsubscribe from previous conversation
      websocketService.unsubscribeFromConversation(currentConversationId.current);
    }
    
    currentConversationId.current = conversationId;
    websocketService.subscribeToConversation(conversationId);
    console.log('📝 Subscribed to conversation:', conversationId);
  }, []);

  // Unsubscribe from conversation
  const unsubscribeFromConversation = useCallback((conversationId: string) => {
    websocketService.unsubscribeFromConversation(conversationId);
    if (currentConversationId.current === conversationId) {
      currentConversationId.current = null;
    }
    console.log('📝 Unsubscribed from conversation:', conversationId);
  }, []);

  // Send message
  const sendMessage = useCallback((content: string, destAddr: string) => {
    if (!currentConversationId.current) {
      console.warn('⚠️ No conversation subscribed, cannot send message');
      return;
    }

    websocketService.send({
      type: 'send_message',
      data: {
        conversation_id: currentConversationId.current,
        content,
        dest_addr: destAddr
      }
    });
    console.log('📤 Message sent via WebSocket');
  }, []);

  // Send typing indicator
  const sendTyping = useCallback((isTyping: boolean) => {
    if (currentConversationId.current) {
      websocketService.sendTyping(currentConversationId.current, isTyping);
    }
  }, []);

  // Reconnect
  const reconnect = useCallback(() => {
    websocketService.reconnect();
  }, []);

  // Setup WebSocket handlers on mount
  useEffect(() => {
    // Add message handlers
    websocketService.addMessageHandler('message.new', handleNewMessage);
    websocketService.addMessageHandler('message.updated', handleMessageUpdated);
    websocketService.addMessageHandler('conversation.updated', handleConversationUpdated);
    websocketService.addMessageHandler('conversation_list.updated', handleConversationListUpdated);
    websocketService.addMessageHandler('typing.start', handleTypingStart);
    websocketService.addMessageHandler('typing.stop', handleTypingStop);
    
    // Add connection handler
    websocketService.addConnectionHandler(handleConnectionChange);

    // Subscribe to conversation if provided
    if (options.conversationId) {
      subscribeToConversation(options.conversationId);
    }

    // Cleanup on unmount
    return () => {
      websocketService.removeMessageHandler('message.new', handleNewMessage);
      websocketService.removeMessageHandler('message.updated', handleMessageUpdated);
      websocketService.removeMessageHandler('conversation.updated', handleConversationUpdated);
      websocketService.removeMessageHandler('conversation_list.updated', handleConversationListUpdated);
      websocketService.removeMessageHandler('typing.start', handleTypingStart);
      websocketService.removeMessageHandler('typing.stop', handleTypingStop);
      websocketService.removeConnectionHandler(handleConnectionChange);

      // Unsubscribe from current conversation
      if (currentConversationId.current) {
        websocketService.unsubscribeFromConversation(currentConversationId.current);
      }
    };
  }, []); // Empty dependency array - handlers are stable

  // Update conversation subscription when conversationId changes
  useEffect(() => {
    if (options.conversationId) {
      subscribeToConversation(options.conversationId);
    }
  }, [options.conversationId, subscribeToConversation]);

  return {
    isConnected,
    sendMessage,
    sendTyping,
    subscribeToConversation,
    unsubscribeFromConversation,
    reconnect
  };
};

export default useWebSocket;
