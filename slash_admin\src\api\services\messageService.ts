import apiClient from "../apiClient";

// Backend API endpoints
export enum MessageApi {
  // Core message endpoints
  getMessages = "api/messages/",
  getMessage = "api/messages/",
  createMessage = "api/messages/",
  updateMessage = "api/messages/",
  deleteMessage = "api/messages/",

  // Conversation endpoints
  getConversations = "api/conversations/",
  getConversation = "api/conversations/",
  createConversation = "api/conversations/",
  updateConversation = "api/conversations/",

  // Case endpoints
  getCases = "api/cases/",
  getCase = "api/cases/",
  createCase = "api/cases/",
  updateCase = "api/cases/",
  caseExport = "api/cases/export/",

  // Contact endpoints
  getContacts = "api/contacts/",
  getContact = "api/contacts/",

  // Queue endpoints
  getMessageQueue = "api/message-queue/",
  createQueueMessage = "api/message-queue/",

  // Utility endpoints
  sendMessage = "api/send-message/",
  receiveMessage = "api/receive-message/",
  bulkMessage = "api/bulk-message/",
  dashboardStats = "api/dashboard-stats/",
  search = "api/search/",
  recentActivity = "api/recent-activity/",
  systemHealth = "api/system-health/",
  messageContacts = "api/message-contacts/",  // New endpoint for unique contacts
}

const MessageApiActions = {
  // Message actions
  markRead: (id: string) => `api/messages/${id}/mark_read/`,
  createCase: (id: string) => `api/messages/${id}/create_case/`,

  // Conversation actions
  conversationMessages: (id: string) => `api/conversations/${id}/messages/`,
  assignConversation: (id: string) => `api/conversations/${id}/assign/`,
  markConversationRead: (id: string) => `api/conversations/${id}/mark_read/`,
  changeConversationStatus: (id: string) => `api/conversations/${id}/change_status/`,

  // Case actions
  assignCase: (id: string) => `api/cases/${id}/assign/`,
  changeCaseStatus: (id: string) => `api/cases/${id}/change_status/`,
  getCaseMessages: (id: string) => `api/cases/${id}/messages/`,

  // Queue actions
  retryQueueMessage: (id: string) => `api/message-queue/${id}/retry/`,
  cancelQueueMessage: (id: string) => `api/message-queue/${id}/cancel/`,

  // Contact actions
  getContactConversations: (id: string) => `api/contacts/${id}/conversations/`,
  optOutContact: (id: string) => `api/contacts/${id}/opt_out/`,
};




interface GetMessagesParams {
  status?: string;
  category?: string;
  priority?: string;
  assigned_to?: string;
  search?: string;
  page?: number;
  page_size?: number;
  tags?: string;
  date_start?: string;
  date_end?: string;
  archived?: boolean;
  ordering?: string;
}

interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Get messages with backend-compatible parameters
const getMessages = async (params?: GetMessagesParams): Promise<PaginatedResponse<any>> => {
  try {
    console.log('messageService.getMessages called with params:', params);

    const response = await apiClient.get<PaginatedResponse<any>>({
      url: MessageApi.getMessages,
      params
    });

    console.log('messageService - Raw API response:', response);
    return response;
  } catch (error: any) {
    console.error('messageService - API Error in getMessages:', error);
    throw error;
  }
};

// Get unique message contacts (for MessageList without repetition)
const getMessageContacts = async (params?: {
  search?: string;
  page?: number;
  page_size?: number;
}): Promise<PaginatedResponse<any>> => {
  try {
    console.log('messageService.getMessageContacts called with params:', params);

    const response = await apiClient.get<PaginatedResponse<any>>({
      url: MessageApi.messageContacts,
      params
    });

    console.log('messageService.getMessageContacts response:', response);
    return response;
  } catch (error: any) {
    console.error('Error fetching message contacts:', error);
    throw error;
  }
};

// Get conversations (new backend endpoint)
const getConversations = async (params?: {
  status?: string;
  priority?: number;
  assigned_to?: string;
  search?: string;
  page?: number;
  page_size?: number;
}): Promise<PaginatedResponse<any>> => {
  try {
    const response = await apiClient.get<PaginatedResponse<any>>({
      url: MessageApi.getConversations,
      params
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching conversations:', error);
    throw error;
  }
};


const getConversation = async (conversationId: string) => {
  try {
    const response = await apiClient.get({
      url: MessageApi.getConversation + conversationId + '/'
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching conversation:', error);
    throw error;
  }
};

// Get conversation messages
const getConversationMessages = async (conversationId: string, params?: {
  page?: number;
  page_size?: number;
}): Promise<PaginatedResponse<any>> => {
  try {
    const response = await apiClient.get<PaginatedResponse<any>>({
      url: MessageApiActions.conversationMessages(conversationId),
      params
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching conversation messages:', error);
    throw error;
  }
};

// Get contacts
const getContacts = async (params?: {
  search?: string;
  opt_in_status?: boolean;
  preferred_language?: string;
  page?: number;
  page_size?: number;
}): Promise<PaginatedResponse<any>> => {
  try {
    const response = await apiClient.get<PaginatedResponse<any>>({
      url: MessageApi.getContacts,
      params
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching contacts:', error);
    throw error;
  }
};

// Get contact by ID
const getContact = async (contactId: string) => {
  try {
    const response = await apiClient.get({
      url: `${MessageApi.getContacts}${contactId}/`
    });
    return response;
  } catch (error) {
    console.error('Error fetching contact:', error);
    throw error;
  }
};

// Create contact
const createContact = async (data: {
  name: string;
  phone_number: string;
  location?: string;
  preferred_language?: string;
  opt_in_status?: boolean;
}) => {
  try {
    const response = await apiClient.post({
      url: MessageApi.getContacts,
      data
    });
    return response;
  } catch (error) {
    console.error('Error creating contact:', error);
    throw error;
  }
};

// Update contact
const updateContact = async (contactId: string, data: {
  name?: string;
  phone_number?: string;
  location?: string;
  preferred_language?: string;
  opt_in_status?: boolean;
}) => {
  try {
    const response = await apiClient.patch({
      url: `${MessageApi.getContacts}${contactId}/`,
      data
    });
    return response;
  } catch (error) {
    console.error('Error updating contact:', error);
    throw error;
  }
};

// Delete contact
const deleteContact = async (contactId: string) => {
  try {
    const response = await apiClient.delete({
      url: `${MessageApi.getContacts}${contactId}/`
    });
    return response;
  } catch (error) {
    console.error('Error deleting contact:', error);
    throw error;
  }
};

// Get cases
const getCases = async (params?: {
  status?: string;
  category?: string;
  priority?: string;
  assigned_to?: string;
  search?: string;
  page?: number;
  page_size?: number;
}): Promise<PaginatedResponse<any>> => {
  try {
    const response = await apiClient.get<PaginatedResponse<any>>({
      url: MessageApi.getCases,
      params
    });
    return response;
  } catch (error: any) {
    console.error('Error fetching cases:', error);
    throw error;
  }
};


interface GetRepliesParams {
  message_id: string;
  page?: number;
  page_size?: number;
  phone_number?: string;
  search?: string;
  sender_type?: 'customer' | 'agent';
}

// Send a new message
const sendMessage = async (data: {
  dest_addr: string;
  content: string;
  source_addr?: string;
  conversation_id?: string;
  priority?: string;
}) => {
  console.log('messageService.sendMessage called with data:', data);
  try {
    const response = await apiClient.post({
      url: MessageApi.sendMessage,
      data
    });
    return response;
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
};

// Send bulk messages
const sendBulkMessage = async (data: {
  recipients: string[];
  content: string;
  source_addr?: string;
  priority?: string;
}) => {
  try {
    const response = await apiClient.post({
      url: MessageApi.bulkMessage,
      data
    });
    return response;
  } catch (error) {
    console.error('Error sending bulk message:', error);
    throw error;
  }
};

// Update message
const updateMessage = async (messageId: string, data: {
  content?: string;
  status?: string;
}) => {
  try {
    const response = await apiClient.patch({
      url: `${MessageApi.updateMessage}${messageId}/`,
      data
    });
    return response;
  } catch (error: any) {
    console.error('Error updating message:', error);
    throw error;
  }
};

// Mark message as read
const markMessageRead = async (messageId: string) => {
  try {
    const response = await apiClient.post({
      url: MessageApiActions.markRead(messageId),
      data: {}
    });
    return response;
  } catch (error) {
    console.error('Error marking message as read:', error);
    throw error;
  }
};

// Create case from message
const createCaseFromMessage = async (messageId: string, data: {
  title?: string;
  description?: string;
  category?: string;
  priority?: string;
}) => {
  console.log('messageService.createCaseFromMessage called with data:', data);
  try {
    const response = await apiClient.post({
      url: MessageApiActions.createCase(messageId),
      data
    });
    return response;
  } catch (error) {
    console.error('Error creating case from message:', error);
    throw error;
  }
};

const createCaseFromMessageTitle = async (data: {
  title?: string;
  description?: string;
  category?: string;
  priority?: string;
  conversation?: string;
  message?: string;
}) => {
  try {
    const response = await apiClient.post({
      url: MessageApi.createCase,
      data
    });
    return response;
  } catch (error) {
    console.error('Error creating case from message:', error);
    throw error;
  }
};

// Create case
const createCase = async (data: {
  title: string;
  description: string;
  case_id: string;
  status?: string;
  priority?: string;
  category?: string;
  internal_notes?: string;
}) => {
  try {
    const response = await apiClient.post({
      url: MessageApi.createCase,
      data
    });
    return response;
  } catch (error) {
    console.error('Error creating case:', error);
    throw error;
  }
};

// Update case
const updateCase = async (caseId: string, data: {
  title?: string;
  description?: string;
  status?: string;
  priority?: string;
  category?: string;
  internal_notes?: string;
}) => {
  try {
    const response = await apiClient.patch({
      url: `${MessageApi.updateCase}${caseId}/`,
      data
    });
    return response;
  } catch (error) {
    console.error('Error updating case:', error);
    throw error;
  }
};

// Delete case
const deleteCase = async (caseId: string) => {
  try {
    const response = await apiClient.delete({
      url: `${MessageApi.updateCase}${caseId}/`
    });
    return response;
  } catch (error) {
    console.error('Error deleting case:', error);
    throw error;
  }
};

// Export cases
const exportCases = async (params: {
  format?: string;
  search?: string;
  status?: string;
  priority?: string;
  category?: string;
  created_after?: string;
  created_before?: string;
  ordering?: string;
  page_size?: string;
  [key: string]: any;
}) => {
  try {
    const response = await apiClient.get({
      url: MessageApi.caseExport, // Use the correct export endpoint
      params: {
        ...params,
        // format: params.format || 'xlsx', // Request Excel format
        page_size: params.page_size || 'all' // Get all records for export
      },
      responseType: 'blob' // Important: Tell axios to expect binary data
    });
    return response;
  } catch (error) {
    console.error('Error exporting cases:', error);
    throw error;
  }
};

const caseExport = async (params: {
  status?: string;
  category?: string;
  priority?: string;
  assigned_to?: string;
  search?: string;
}) => {
  try {
    const response = await apiClient.get({
      url: MessageApi.caseExport,
      params
    });
    return response;
  } catch (error) {
    console.error('Error exporting cases:', error);
    throw error;
  }
};


// Assign conversation
const assignConversation = async (conversationId: string, userId: string) => {
  try {
    const response = await apiClient.post({
      url: MessageApiActions.assignConversation(conversationId),
      data: { user_id: userId }
    });
    return response;
  } catch (error) {
    console.error('Error assigning conversation:', error);
    throw error;
  }
};

// Change conversation status
const changeConversationStatus = async (conversationId: string, status: string) => {
  try {
    const response = await apiClient.post({
      url: MessageApiActions.changeConversationStatus(conversationId),
      data: { status }
    });
    return response;
  } catch (error) {
    console.error('Error changing conversation status:', error);
    throw error;
  }
};

// Delete conversation
const deleteConversation = async (conversationId: string) => {
  try {
    const response = await apiClient.delete({
      url: `${MessageApi.getConversations}${conversationId}/`
    });
    return response;
  } catch (error) {
    console.error('Error deleting conversation:', error);
    throw error;
  }
};

// Mark conversation as read
const markConversationRead = async (conversationId: string) => {
  try {
    const response = await apiClient.post({
      url: MessageApiActions.markConversationRead(conversationId),
      data: {}
    });
    return response;
  } catch (error) {
    console.error('Error marking conversation as read:', error);
    throw error;
  }
};

// Export conversation as PDF
const exportConversationPDF = async (conversationId: string) => {
  try {
    const response = await apiClient.get({
      url: `${MessageApi.getConversations}${conversationId}/export_pdf/`,
      responseType: 'blob'
    });

    // Create download link
    const blob = new Blob([response], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `conversation_${conversationId}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    return response;
  } catch (error) {
    console.error('Error exporting conversation PDF:', error);
    throw error;
  }
};

// Assign case
const assignCase = async (caseId: string, userId: string) => {
  try {
    const response = await apiClient.post({
      url: MessageApiActions.assignCase(caseId),
      data: { user_id: userId }
    });
    return response;
  } catch (error) {
    console.error('Error assigning case:', error);
    throw error;
  }
};

// Change case status
const changeCaseStatus = async (caseId: string, status: string) => {
  try {
    const response = await apiClient.post({
      url: MessageApiActions.changeCaseStatus(caseId),
      data: { status }
    });
    return response;
  } catch (error) {
    console.error('Error changing case status:', error);
    throw error;
  }
};

// Get dashboard statistics
const getDashboardStats = async () => {
  try {
    const response = await apiClient.get({
      url: MessageApi.dashboardStats
    });
    return response;
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    throw error;
  }
};

// Search across all entities
const searchAll = async (query: string) => {
  try {
    const response = await apiClient.get({
      url: MessageApi.search,
      params: { q: query }
    });
    return response;
  } catch (error) {
    console.error('Error searching:', error);
    throw error;
  }
};

// Legacy functions (updated to work with backend)
const getReplies = async (params: GetRepliesParams): Promise<PaginatedResponse<any>> => {
  const { message_id, ...queryParams } = params;
  try {
    // Get conversation messages instead of replies
    const message = await apiClient.get({
      url: `${MessageApi.getMessage}${message_id}/`
    });

    if (message.conversation) {
      const response = await apiClient.get<PaginatedResponse<any>>({
        url: MessageApiActions.conversationMessages(message.conversation.id),
        params: queryParams
      });
      return response;
    }

    return { count: 0, next: null, previous: null, results: [] };
  } catch (error) {
    console.error('getReplies error:', error);
    throw error;
  }
};

const addReply = async (messageId: string, content: string) => {
  try {
    // Get the original message to find conversation and destination
    const message = await apiClient.get({
      url: `${MessageApi.getMessage}${messageId}/`
    });

    console.log('Original message for reply:', message);

    if (message.conversation) {
      // conversation is a string ID, not an object
      const conversationId = typeof message.conversation === 'string'
        ? message.conversation
        : message.conversation.id;

      const response = await sendMessage({
        dest_addr: message.source_addr, // Reply to the sender
        content,
        conversation_id: conversationId
      });

      console.log('Reply sent successfully:', response);
      return response;
    }

    throw new Error('Message has no conversation');
  } catch (error) {
    console.error('addReply error:', error);
    throw error;
  }
};

const deleteMessage = async (id: string) => {
  try {
    return await apiClient.delete({
      url: `${MessageApi.deleteMessage}${id}/`
    });
  } catch (error) {
    console.error('Error deleting message:', error);
    throw error;
  }
};



const messageService = {
  // Core message operations
  getMessages,
  getMessageContacts,  // New function for unique contacts
  addReply,
  updateMessage,       // New function for editing messages
  deleteMessage,
  getReplies,
  sendMessage,
  sendBulkMessage,
  markMessageRead,
  createCaseFromMessage,
  createCaseFromMessageTitle, 

  // Conversation operations
  getConversations,
  getConversation,
  getConversationMessages,
  assignConversation,
  changeConversationStatus,
  deleteConversation,
  markConversationRead,
  exportConversationPDF,

  // Case operations
  getCases,
  assignCase,
  createCase,
  updateCase,
  deleteCase,
  changeCaseStatus,
  caseExport,
  exportCases,

  // Contact operations
  getContacts,
  getContact,
  createContact,
  updateContact,
  deleteContact,

  // Utility operations
  getDashboardStats,
  searchAll,
};

export default messageService;










