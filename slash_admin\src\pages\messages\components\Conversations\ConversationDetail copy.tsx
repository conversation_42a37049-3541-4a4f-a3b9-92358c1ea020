import React, { useState, useEffect, useRef } from 'react';
import { Conversation, Message, User } from '../../../../types';
import {
  UserIcon,
  PhoneIcon,
  MapPinIcon,
  ChatBubbleLeftIcon,
  ChatBubbleLeftEllipsisIcon,
  PaperAirplaneIcon,
  UserPlusIcon,
  CheckCircleIcon,
  TrashIcon,
  DocumentArrowDownIcon,
  EllipsisVerticalIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';
import messageService from '../../../../api/services/messageService';
import { toast } from 'sonner';
import CreateCaseModal from '../../../../components/modals/CreateCaseModal';
import MessageActions from '../Messages/MessageActions';

interface ConversationDetailProps {
  conversation: Conversation;
}

export default function ConversationDetail({ conversation }: ConversationDetailProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [replyContent, setReplyContent] = useState('');
  const [sending, setSending] = useState(false);

  // Context menu state
  const [contextMenu, setContextMenu] = useState<{
    show: boolean;
    x: number;
    y: number;
    messageId: string;
    messageDirection: string;
    messageContent: string;
  } | null>(null);

  // Edit message state
  const [editingMessage, setEditingMessage] = useState<{
    id: string;
    content: string;
  } | null>(null);

  // Create case modal state
  const [showCreateCaseModal, setShowCreateCaseModal] = useState(false);
  const [selectedMessageForCase, setSelectedMessageForCase] = useState<Message | null>(null);

  // Conversation actions state
  const [isDeleting, setIsDeleting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Refs for scrolling
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom function
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Scroll to bottom on initial load and when messages update
  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages]);

  // Mark messages as read
  const markMessagesAsRead = async () => {
    try {
      console.log('________________Marking messages as read:', messages);
      // Mark all inbound messages in this conversation as read
      const unreadMessages = messages.filter(msg =>
        msg.direction === 'inbound' && !(msg as any).read_at
      );

      for (const message of unreadMessages) {
        console.log('Marking message as read:', message.id);
        await messageService.markMessageRead(message.id);
      }

      // Update local state
      setMessages(prev => prev.map(msg => ({
        ...msg,
        read_at: msg.direction === 'inbound' && !(msg as any).read_at ? new Date().toISOString() : (msg as any).read_at
      } as any)));

    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  };

  // Fetch conversation messages
  useEffect(() => {
    const fetchMessages = async () => {
      setLoading(true);
      try {
        console.log('Fetching messages for conversation:', conversation.id);
        const response = await messageService.getConversationMessages(conversation.id);
        console.log('Conversation messages response:', response);
        setMessages(response.results || response || []);

        // Mark messages as read after a short delay
        setTimeout(() => {
          markMessagesAsRead();
        }, 1000);

      } catch (error) {
        console.error('Error fetching conversation messages:', error);
        toast.error('Failed to load messages');
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();
  }, [conversation.id]);

  // Context menu handlers
  const handleRightClick = (e: React.MouseEvent, message: Message) => {
    e.preventDefault();
    setContextMenu({
      show: true,
      x: e.clientX,
      y: e.clientY,
      messageId: message.id,
      messageDirection: message.direction,
      messageContent: message.content
    });
  };

  const closeContextMenu = () => {
    setContextMenu(null);
  };

  // Edit message
  const handleEditMessage = () => {
    if (contextMenu) {
      setEditingMessage({
        id: contextMenu.messageId,
        content: contextMenu.messageContent
      });
      closeContextMenu();
    }
  };

  const saveEditedMessage = async () => {
    if (!editingMessage) return;

    try {
      await messageService.updateMessage(editingMessage.id, {
        content: editingMessage.content
      });

      // Update local state
      setMessages(prev => prev.map(msg =>
        msg.id === editingMessage.id
          ? { ...msg, content: editingMessage.content }
          : msg
      ));

      setEditingMessage(null);
      toast.success('Message updated successfully');
    } catch (error) {
      console.error('Error updating message:', error);
      toast.error('Failed to update message');
    }
  };

  const cancelEdit = () => {
    setEditingMessage(null);
  };

  // Delete message
  const handleDeleteMessage = async () => {
    if (!contextMenu) return;

    if (window.confirm('Are you sure you want to delete this message?')) {
      try {
        await messageService.deleteMessage(contextMenu.messageId);

        // Remove from local state
        setMessages(prev => prev.filter(msg => msg.id !== contextMenu.messageId));

        toast.success('Message deleted successfully');
      } catch (error) {
        console.error('Error deleting message:', error);
        toast.error('Failed to delete message');
      }
    }
    closeContextMenu();
  };

  // Create case from message
  const handleCreateCase = () => {
    if (!contextMenu) return;

    // Find the message object
    const message = messages.find(msg => msg.id === contextMenu.messageId);
    if (message) {
      setSelectedMessageForCase(message);
      setShowCreateCaseModal(true);
    }
    closeContextMenu();
  };

  // Handle case creation from modal
  const handleCaseSubmit = async (caseData: {
    title: string;
    description: string;
    status?: string;
    category?: string;
    priority?: string;
    tags?: string[];
    internal_notes?: string;
  }) => {
    if (!selectedMessageForCase) return;

    try {
      const response = await messageService.createCaseFromMessageTitle({
        title: caseData.title,
        description: caseData.description,
        category: caseData.category || 'general',
        priority: caseData.priority || 'medium',
        // status: caseData.status || 'open', // Remove this as it's not in the API interface
        // tags: caseData.tags?.join(',') || '', // Remove this as it's not in the API interface
        // internal_notes: caseData.internal_notes || '', // Remove this as it's not in the API interface
        conversation: conversation.id,
        message: selectedMessageForCase.id
      });

      toast.success(`Case created successfully: ${response.case_id}`);
      setShowCreateCaseModal(false);
      setSelectedMessageForCase(null);
    } catch (error) {
      console.error('Error creating case:', error);
      throw error; // Let the modal handle the error
    }
  };

  // Send reply
  const handleSendReply = async () => {
    if (!replyContent.trim()) return;

    setSending(true);
    try {
      await messageService.sendMessage({
        dest_addr: conversation.contact_phone_number || conversation.contact?.phone_number,
        content: replyContent,
        conversation_id: conversation.id
      });
      
      setReplyContent('');
      toast.success('Reply sent successfully');
      
      // Refresh messages
      const response = await messageService.getConversationMessages(conversation.id);
      setMessages(response.results || response || []);
      
      // Scroll to bottom after sending
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    } catch (error) {
      console.error('Error sending reply:', error);
      toast.error('Failed to send reply');
    } finally {
      setSending(false);
    }
  };

  // Assign conversation
  const handleAssign = async (userId: string) => {
    try {
      await messageService.assignConversation(conversation.id, userId);
      toast.success('Conversation assigned successfully');
    } catch (error) {
      console.error('Error assigning conversation:', error);
      toast.error('Failed to assign conversation');
    }
  };

  // Change status
  const handleStatusChange = async (status: string) => {
    try {
      await messageService.changeConversationStatus(conversation.id, status);
      toast.success('Status updated successfully');
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Failed to update status');
    }
  };

  const formatMessageTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered': return 'text-green-600';
      case 'enroute': return 'text-yellow-600';
      case 'undeliverable': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  // Delete conversation
  const handleDeleteConversation = async () => {
    setIsDeleting(true);
    try {
      await messageService.deleteConversation(conversation.id);
      toast.success('Conversation deleted successfully');
      // Navigate back or refresh parent component
      window.location.reload(); // Simple approach - you might want to use proper navigation
    } catch (error) {
      console.error('Error deleting conversation:', error);
      toast.error('Failed to delete conversation');
    } finally {
      setIsDeleting(false);
    }
  };

  // Export conversation as PDF
  const handleExportPDF = async () => {
    setIsExporting(true);
    try {
      await messageService.exportConversationPDF(conversation.id);
      toast.success('Conversation exported successfully');
    } catch (error) {
      console.error('Error exporting conversation:', error);
      toast.error('Failed to export conversation');
    } finally {
      setIsExporting(false);
    }
  };

  // Mark conversation as read
  const handleMarkAsRead = async () => {
    try {
      await messageService.markConversationRead(conversation.id);
      toast.success('Conversation marked as read');
      // Update local state
      setMessages(prev => prev.map(msg => ({
        ...msg,
        read_at: msg.direction === 'inbound' && !(msg as any).read_at ? new Date().toISOString() : (msg as any).read_at
      } as any)));
    } catch (error) {
      console.error('Error marking conversation as read:', error);
      toast.error('Failed to mark conversation as read');
    }
  };

  // Message actions handlers for MessageActions component
  const handleMessageArchive = async (messageId: string) => {
    try {
      // Implement message archive functionality
      toast.success('Message archived');
    } catch (error) {
      toast.error('Failed to archive message');
    }
  };

  const handleMessageDelete = async (messageId: string) => {
    try {
      // Implement message delete functionality
      toast.success('Message deleted');
    } catch (error) {
      toast.error('Failed to delete message');
    }
  };

  const handleMessageRestore = async (messageId: string) => {
    try {
      // Implement message restore functionality
      toast.success('Message restored');
    } catch (error) {
      toast.error('Failed to restore message');
    }
  };

  const handleMessageExportPDF = async (messageId: string) => {
    try {
      // Implement individual message PDF export
      toast.success('Message exported');
    } catch (error) {
      toast.error('Failed to export message');
    }
  };

  const handleMessageAddTag = async (messageId: string, tag: string) => {
    try {
      // Implement message tagging functionality
      toast.success(`Tag "${tag}" added to message`);
    } catch (error) {
      toast.error('Failed to add tag');
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b border-gray-200 bg-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                <UserIcon className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div>
              <h2 className="text-lg font-medium text-gray-900">
                {conversation.contact_name || conversation.contact?.name || 'Unknown Contact'}
              </h2>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <PhoneIcon className="h-4 w-4" />
                  <span>{conversation.contact_phone_number || conversation.contact?.phone_number || 'Unknown Number'}</span>
                </div>
                {(conversation.contact?.location) && (
                  <div className="flex items-center space-x-1">
                    <MapPinIcon className="h-4 w-4" />
                    <span>{conversation.contact?.location}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Mark as Read Button */}
            {conversation.unread_count > 0 && (
              <button
                onClick={handleMarkAsRead}
                className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                title="Mark as Read"
              >
                <EyeIcon className="h-4 w-4 mr-1" />
                Mark Read
              </button>
            )}

            {/* Export PDF Button */}
            <button
              onClick={handleExportPDF}
              disabled={isExporting}
              className="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              title="Export as PDF"
            >
              <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
              {isExporting ? 'Exporting...' : 'Export PDF'}
            </button>

            {/* Status dropdown */}
            <select
              value={conversation.status}
              onChange={(e) => handleStatusChange(e.target.value)}
              className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="active">Active</option>
              <option value="pending">Pending</option>
              <option value="resolved">Resolved</option>
              <option value="closed">Closed</option>
              <option value="spam">Spam</option>
            </select>

            {/* More Actions Dropdown */}
            <div className="relative">
              <button
                onClick={() => {
                  // Toggle dropdown menu
                  const dropdown = document.getElementById('conversation-actions-dropdown');
                  if (dropdown) {
                    dropdown.classList.toggle('hidden');
                  }
                }}
                className="inline-flex items-center px-2 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                title="More Actions"
              >
                <EllipsisVerticalIcon className="h-4 w-4" />
              </button>

              {/* Dropdown Menu */}
              <div
                id="conversation-actions-dropdown"
                className="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"
              >
                <div className="py-1">
                  <button
                    onClick={() => {
                      if (window.confirm('Are you sure you want to delete this conversation? This action cannot be undone.')) {
                        handleDeleteConversation();
                      }
                      document.getElementById('conversation-actions-dropdown')?.classList.add('hidden');
                    }}
                    disabled={isDeleting}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50 disabled:opacity-50"
                  >
                    <TrashIcon className="h-4 w-4 mr-2" />
                    {isDeleting ? 'Deleting...' : 'Delete Conversation'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-6 space-y-4 flex flex-col-reverse"
      >
        {loading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex space-x-3">
                  <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-16 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center py-8">
            <ChatBubbleLeftIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No messages</h3>
            <p className="mt-1 text-sm text-gray-500">
              This conversation has no messages yet.
            </p>
          </div>
        ) : (
          <>
            {/* Invisible element at the bottom for scrolling */}
            <div ref={messagesEndRef} />
            
            {/* Messages in reverse order (newest at bottom) */}
            {[...messages].reverse().map((message) => (
              <div
                key={message.id}
                className={`flex ${message.direction === 'outbound' ? 'justify-end' : 'justify-start'} group`}
              >
                <div className={`flex items-start space-x-2 ${message.direction === 'outbound' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg cursor-pointer hover:opacity-90 ${
                      message.direction === 'outbound'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}
                    onContextMenu={(e) => handleRightClick(e, message)}
                  >
                    {/* Edit mode */}
                    {editingMessage?.id === message.id ? (
                      <div className="space-y-2">
                        <textarea
                          value={editingMessage.content}
                          onChange={(e) => setEditingMessage({
                            ...editingMessage,
                            content: e.target.value
                          })}
                          className="w-full text-sm bg-white text-gray-900 border border-gray-300 rounded px-2 py-1 resize-none"
                          rows={3}
                        />
                        <div className="flex space-x-2">
                          <button
                            onClick={saveEditedMessage}
                            className="px-2 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600"
                          >
                            Save
                          </button>
                          <button
                            onClick={cancelEdit}
                            className="px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    ) : (
                      <>
                        <div className="text-sm">{message.content}</div>
                        <div className={`text-xs mt-1 flex items-center justify-between ${
                          message.direction === 'outbound' ? 'text-blue-100' : 'text-gray-500'
                        }`}>
                          <span>{formatMessageTime(message.created_at)}</span>
                          {message.direction === 'outbound' && (
                            <span className={`ml-2 ${getStatusColor(message.status)}`}>
                              {message.status === 'delivered' && <CheckCircleIcon className="h-3 w-3" />}
                              {message.status}
                            </span>
                          )}
                        </div>
                      </>
                    )}
                  </div>

                  {/* Message Actions - Show on hover */}
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center">
                    <MessageActions
                      message={message as any}
                      onArchive={handleMessageArchive}
                      onDelete={handleMessageDelete}
                      onRestore={handleMessageRestore}
                      onExportPDF={handleMessageExportPDF}
                      onAddTag={handleMessageAddTag}
                    />
                  </div>
                </div>
              </div>
            ))}
          </>
        )}
      </div>

      {/* Reply input */}
      <div className="border-t border-gray-200 bg-white px-6 py-4">
        <div className="flex space-x-3">
          <div className="flex-1">
            <textarea
              value={replyContent}
              onChange={(e) => setReplyContent(e.target.value)}
              placeholder="Type your reply..."
              rows={3}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            />
          </div>
          <button
            onClick={handleSendReply}
            disabled={!replyContent.trim() || sending}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <PaperAirplaneIcon className="h-4 w-4 mr-1" />
            {sending ? 'Sending...' : 'Send'}
          </button>
        </div>
        
        {/* Quick actions */}
        <div className="mt-3 flex space-x-2">
          <button
            onClick={() => handleStatusChange('resolved')}
            className="text-xs text-green-600 hover:text-green-800"
          >
            Mark Resolved
          </button>
          <button
            onClick={() => handleStatusChange('closed')}
            className="text-xs text-gray-600 hover:text-gray-800"
          >
            Close Conversation
          </button>
        </div>
      </div>

      {/* Context Menu */}
      {contextMenu && (
        <>
          {/* Backdrop to close menu */}
          <div
            className="fixed inset-0 z-40"
            onClick={closeContextMenu}
          />

          {/* Context Menu */}
          <div
            className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-[150px]"
            style={{
              left: contextMenu.x,
              top: contextMenu.y,
            }}
          >
            {/* Edit - only for outbound messages */}
            {contextMenu.messageDirection === 'outbound' && (
              <button
                onClick={handleEditMessage}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit
              </button>
            )}

            {/* Delete - only for outbound messages */}
            {contextMenu.messageDirection === 'outbound' && (
              <button
                onClick={handleDeleteMessage}
                className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Delete
              </button>
            )}

            {/* Create Case - only for inbound messages */}
            {contextMenu.messageDirection === 'inbound' && (
              <button
                onClick={handleCreateCase}
                className="w-full text-left px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Create Case
              </button>
            )}
          </div>
        </>
      )}

      {/* Create Case Modal */}
      <CreateCaseModal
        isOpen={showCreateCaseModal}
        onClose={() => {
          setShowCreateCaseModal(false);
          setSelectedMessageForCase(null);
        }}
        onSubmit={handleCaseSubmit}
        messageContent={selectedMessageForCase?.content}
      />
    </div>
  );
}
