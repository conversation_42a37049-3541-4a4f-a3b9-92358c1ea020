import { Helmet } from "react-helmet-async";

// import Logo from "@/assets/images/logo.png";
import Logo from "@/assets/images/eeu_logo.png";
import Router from "@/router/index";

import { MotionLazy } from "./components/animate/motion-lazy";
import Toast from "./components/toast";
import { AntdAdapter } from "./theme/adapter/antd.adapter";
import { ThemeProvider } from "./theme/theme-provider";
import { AuthProvider } from "./contexts/AuthContext";

function App() {
	return (
		<AuthProvider>
		<ThemeProvider adapters={[AntdAdapter]}>
			<MotionLazy>
				<Helmet>
					{/* <title>Slash Admin</title> 
					<link rel="icon" href={Logo} /> */}
					<title>SMS System</title>
					<link rel="icon" href={Logo} />
				</Helmet>
				<Toast />

				<Router />
			</MotionLazy>
		</ThemeProvider>
		</AuthProvider>
	);
}

export default App;
