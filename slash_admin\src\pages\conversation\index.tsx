import React, { useEffect, useState } from 'react';
import {
  ChatBubbleLeftRightIcon,
  WifiIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { toast } from 'sonner';

import ConversationList from '../messages/components/Conversations/ConversationList';
import ConversationDetail from '../messages/components/Conversations/ConversationDetail';
import { Conversation } from '@/types';
import { useConversationList } from '../../hooks/useConversationList';


export default function Messages() {
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);

  // Use real-time conversation list hook
  const {
    conversations,
    loading,
    error,
    currentPage,
    totalPages,
    totalCount,
    itemsPerPage,
    setCurrentPage,
    isConnected
  } = useConversationList({
    itemsPerPage: 20,
    autoRefresh: true
  });

  // Show error toast if there's an error
  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);




  return (
      <div className="h-[calc(100vh-72px)] flex">
              <div className="w-1/3 border-r border-gray-200 bg-gray-50 overflow-hidden flex flex-col">
                {/* Real-time connection status */}
                <div className="px-4 py-2 bg-white border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-gray-900">Conversations</h2>
                    <div className="flex items-center space-x-2">
                      {isConnected ? (
                        <div className="flex items-center text-green-600">
                          <WifiIcon className="h-4 w-4 mr-1" />
                          <span className="text-xs">Real-time</span>
                        </div>
                      ) : (
                        <div className="flex items-center text-red-600">
                          <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                          <span className="text-xs">Offline</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <ConversationList
                  conversations={conversations}
                  onSelectConversation={setSelectedConversation}
                  selectedConversationId={selectedConversation?.id}
                  loading={loading}
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalCount={totalCount}
                  itemsPerPage={itemsPerPage}
                  onPageChange={setCurrentPage}
                />
              </div>
              <div className="flex-1 flex flex-col overflow-hidden bg-white">
                {selectedConversation ? (
                  <ConversationDetail conversation={selectedConversation} />
                ) : (
                  <div className="flex-1 flex items-center justify-center">
                    <div className="text-center max-w-md">
                      <div className="mx-auto mb-6 p-4 bg-gray-100 rounded-full w-fit">
                        <ChatBubbleLeftRightIcon className="h-12 w-12 text-gray-400" />
                      </div>
                      <h3 className="text-xl font-medium text-gray-900 mb-2">
                        Select a conversation
                      </h3>
                      <p className="text-gray-500">
                        Choose a conversation to view the message thread and details.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
  );
}
