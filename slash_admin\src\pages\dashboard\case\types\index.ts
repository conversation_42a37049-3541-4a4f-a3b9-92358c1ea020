// Core entity types for the compliance case management system

export interface Tenant {
  id: number;
  name: string;
  slug: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Role {
  id: number;
  tenantId: number;
  name: string;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
}

export interface User {
  id: number;
  tenantId: number;
  name: string;
  email: string;
  roleId: number;
  role?: Role;
  status: 'active' | 'inactive' | 'suspended';
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Party {
  id: number;
  tenantId: number;
  type: 'Person' | 'Organization';
  name: string;
  contact: {
    email?: string;
    phone?: string;
    address?: string;
    [key: string]: any;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CaseParty {
  id: number;
  caseId: number;
  partyId: number;
  party?: Party;
  role: 'Complainant' | 'Subject' | 'Witness' | 'Assignee' | 'Observer';
  createdAt: string;
}

export interface Case {
  id: number;
  tenantId: number;
  caseNo: string;
  title: string;
  description: string;
  category: 'Compliance' | 'Complaint' | 'Audit' | 'Incident';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  status: 'New' | 'UnderReview' | 'Investigating' | 'AwaitingAction' | 'Resolved' | 'Closed' | 'Archived';
  reporterType: 'Employee' | 'Citizen' | 'Anonymous' | 'Regulator';
  reporterContact: {
    name?: string;
    email?: string;
    phone?: string;
    anonymous?: boolean;
    [key: string]: any;
  };
  createdBy: number;
  createdByUser?: User;
  assignedTo?: number;
  assignedToUser?: User;
  parties?: CaseParty[];
  events?: CaseEvent[];
  tasks?: Task[];
  documents?: Document[];
  slaFirstResponseDue?: string;
  slaResolutionDue?: string;
  isOverdue?: boolean;
  isRestricted?: boolean;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  closedAt?: string;
}

export interface CaseEvent {
  id: number;
  caseId: number;
  type: 'StatusChange' | 'Assignment' | 'Note' | 'Email' | 'Upload' | 'DeadlineChange' | 'TaskCreated' | 'TaskCompleted';
  data: {
    oldValue?: any;
    newValue?: any;
    note?: string;
    taskId?: number;
    documentId?: number;
    [key: string]: any;
  };
  createdBy: number;
  createdByUser?: User;
  createdAt: string;
}

export interface Task {
  id: number;
  caseId: number;
  title: string;
  description?: string;
  type: 'Interview' | 'EvidenceReview' | 'CorrectiveAction' | 'Approval' | 'Investigation' | 'Documentation';
  assigneeId?: number;
  assignee?: User;
  dueAt?: string;
  status: 'Open' | 'InProgress' | 'Blocked' | 'Done';
  checklist?: TaskChecklistItem[];
  attachments?: number[];
  createdBy: number;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface TaskChecklistItem {
  id: number;
  text: string;
  completed: boolean;
  completedAt?: string;
  completedBy?: number;
}

export interface Document {
  id: number;
  caseId: number;
  fileKey: string;
  filename: string;
  mimeType: string;
  size: number;
  uploadedBy: number;
  uploadedByUser?: User;
  hash: string;
  tags: string[];
  isEvidence: boolean;
  description?: string;
  createdAt: string;
}

export interface SLA {
  id: number;
  tenantId: number;
  name: string;
  appliesTo: {
    categories?: string[];
    priorities?: string[];
    reporterTypes?: string[];
    [key: string]: any;
  };
  firstResponseHours: number;
  resolutionHours: number;
  escalationRules?: {
    hours: number;
    action: string;
    recipients: number[];
  }[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ReportConfig {
  id: number;
  tenantId: number;
  name: string;
  description?: string;
  query: {
    filters: {
      status?: string[];
      category?: string[];
      priority?: string[];
      dateRange?: {
        start: string;
        end: string;
      };
      [key: string]: any;
    };
    groupBy?: string[];
    metrics?: string[];
  };
  schedule?: string; // cron expression
  recipients: string[];
  format: 'CSV' | 'PDF' | 'Excel';
  isActive: boolean;
  lastRun?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Notification {
  id: number;
  tenantId: number;
  channel: 'Email' | 'Webhook' | 'SMS' | 'InApp';
  payload: {
    to?: string[];
    subject?: string;
    body?: string;
    url?: string;
    caseId?: number;
    [key: string]: any;
  };
  status: 'Pending' | 'Sent' | 'Failed' | 'Delivered';
  error?: string;
  sentAt?: string;
  createdAt: string;
}

export interface DashboardMetrics {
  totalCases: number;
  openCases: number;
  overdueCases: number;
  resolvedThisMonth: number;
  averageResolutionTime: number;
  slaBreaches: number;
  casesByStatus: Record<string, number>;
  casesByCategory: Record<string, number>;
  casesByPriority: Record<string, number>;
  monthlyTrends: Array<{
    month: string;
    created: number;
    resolved: number;
    breaches: number;
  }>;
  topAssignees: Array<{
    userId: number;
    name: string;
    caseCount: number;
    avgResolutionTime: number;
  }>;
}

export interface CaseFilters {
  status?: string[];
  category?: string[];
  priority?: string[];
  assignedTo?: number[];
  reporterType?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  search?: string;
  isOverdue?: boolean;
  isRestricted?: boolean;
}

export interface IntakeForm {
  title: string;
  description: string;
  category: Case['category'];
  priority: Case['priority'];
  reporterType: Case['reporterType'];
  reporterContact: Case['reporterContact'];
  attachments?: File[];
  anonymous?: boolean;
}

export interface WorkflowStep {
  id: string;
  name: string;
  status: Case['status'];
  description: string;
  requiredRoles: string[];
  nextSteps: string[];
  slaHours?: number;
  autoActions?: string[];
}