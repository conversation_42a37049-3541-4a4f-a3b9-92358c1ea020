/**
 * WebSocket Service for Real-time Communication
 * Handles WebSocket connections for live messaging
 */

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp?: string;
}

export interface MessageData {
  id: string;
  conversation_id: string;
  content: string;
  direction: 'inbound' | 'outbound';
  source_addr: string;
  dest_addr: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface ConversationUpdateData {
  conversation_id: string;
  unread_count: number;
  last_message_at: string;
  status: string;
}

export interface ConversationListUpdateData {
  type: 'conversation_update';
  conversation_id: string;
  last_message: {
    content: string;
    direction: 'inbound' | 'outbound';
    created_at: string;
    source_addr: string;
  };
  contact_name: string;
  contact_phone: string;
  unread_count_increment: number;
}

class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private messageHandlers: Map<string, Set<(data: any) => void>> = new Map();
  private connectionHandlers: Set<(connected: boolean) => void> = new Set();
  private isConnecting = false;
  private shouldReconnect = true;

  constructor() {
    this.connect();
  }

  /**
   * Establish WebSocket connection
   */
  private connect() {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.CONNECTING)) {
      return;
    }

    this.isConnecting = true;
    
    try {
      // Determine WebSocket URL based on current location
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';

      // For development with Django backend
      const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';
      const wsUrl = isDevelopment
        // ? 'ws://localhost:8000/ws/messages/'  // Django default port
        ? 'ws://***********:7003/ws/messages/'  
        : `${protocol}//${window.location.host}/ws/messages/`;
      
      console.log('🔌 Connecting to WebSocket:', wsUrl);
      
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);
      
    } catch (error) {
      console.error('❌ WebSocket connection error:', error);
      this.isConnecting = false;
      this.scheduleReconnect();
    }
  }

  /**
   * Handle WebSocket connection open
   */
  private handleOpen(event: Event) {
    console.log('✅ WebSocket connected successfully');
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    
    // Start heartbeat
    this.startHeartbeat();
    
    // Notify connection handlers
    this.connectionHandlers.forEach(handler => handler(true));
    
    // Send authentication if needed
    this.authenticate();
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(event: MessageEvent) {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      console.log('📨 WebSocket message received:', message);
      
      // Handle different message types
      switch (message.type) {
        case 'connection.established':
          console.log('🎉 Connection established:', message.data);
          break;
        case 'auth.success':
          console.log('🔐 Authentication successful:', message.data);
          break;
        case 'subscription.success':
          console.log('📝 Subscription successful:', message.data);
          break;
        case 'messages_subscription.success':
          console.log('📝 General messages subscription successful:', message.data);
          break;
        case 'conversations_subscription.success':
          console.log('📝 Conversation list subscription successful:', message.data);
          break;
        case 'conversation_list.updated':
          this.notifyHandlers('conversation_list.updated', message.data);
          break;
        case 'message.new':
          this.notifyHandlers('message.new', message.data);
          break;
        case 'message.updated':
          this.notifyHandlers('message.updated', message.data);
          break;
        case 'conversation.updated':
          this.notifyHandlers('conversation.updated', message.data);
          break;
        case 'typing.start':
          this.notifyHandlers('typing.start', message.data);
          break;
        case 'typing.stop':
          this.notifyHandlers('typing.stop', message.data);
          break;
        case 'heartbeat.ping':
          // Respond to server heartbeat ping
          this.send({ type: 'heartbeat_response', data: {} });
          break;
        case 'heartbeat.response':
          console.log('💓 Heartbeat response received');
          break;
        case 'error':
          console.error('❌ Server error:', message.data);
          break;
        default:
          console.log('🔄 Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('❌ Error parsing WebSocket message:', error);
    }
  }

  /**
   * Handle WebSocket connection close
   */
  private handleClose(event: CloseEvent) {
    console.log('🔌 WebSocket connection closed:', event.code, event.reason);
    this.isConnecting = false;
    
    // Stop heartbeat
    this.stopHeartbeat();
    
    // Notify connection handlers
    this.connectionHandlers.forEach(handler => handler(false));
    
    // Attempt to reconnect if not intentionally closed
    if (this.shouldReconnect && event.code !== 1000) {
      this.scheduleReconnect();
    }
  }

  /**
   * Handle WebSocket errors
   */
  private handleError(event: Event) {
    console.error('❌ WebSocket error:', event);
    this.isConnecting = false;
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    console.log(`🔄 Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${this.reconnectInterval}ms`);
    
    setTimeout(() => {
      if (this.shouldReconnect) {
        this.connect();
      }
    }, this.reconnectInterval);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({ type: 'heartbeat', data: {} });
      }
    }, 30000); // Send heartbeat every 30 seconds
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Send authentication message and subscribe to general messages
   */
  private authenticate() {
    // Get auth token from localStorage or wherever it's stored
    const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');

    if (token) {
      this.send({
        type: 'authenticate',
        data: { token }
      });
    }

    // Subscribe to general messages for real-time SMS updates
    this.send({
      type: 'subscribe_messages',
      data: {}
    });

    // Subscribe to conversation list updates
    this.send({
      type: 'subscribe_conversations',
      data: { user_id: 'current_user' } // In a real app, use actual user ID
    });

    console.log('📝 Subscribed to general messages and conversation list for real-time updates');
  }

  /**
   * Send message through WebSocket
   */
  public send(message: WebSocketMessage) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const messageWithTimestamp = {
        ...message,
        timestamp: new Date().toISOString()
      };
      
      this.ws.send(JSON.stringify(messageWithTimestamp));
      console.log('📤 WebSocket message sent:', messageWithTimestamp);
    } else {
      console.warn('⚠️ WebSocket not connected, message not sent:', message);
    }
  }

  /**
   * Subscribe to conversation messages
   */
  public subscribeToConversation(conversationId: string) {
    this.send({
      type: 'subscribe_conversation',
      data: { conversation_id: conversationId }
    });
  }

  /**
   * Unsubscribe from conversation messages
   */
  public unsubscribeFromConversation(conversationId: string) {
    this.send({
      type: 'unsubscribe_conversation',
      data: { conversation_id: conversationId }
    });
  }

  /**
   * Send typing indicator
   */
  public sendTyping(conversationId: string, isTyping: boolean) {
    this.send({
      type: isTyping ? 'typing_start' : 'typing_stop',
      data: { conversation_id: conversationId }
    });
  }

  /**
   * Add message handler
   */
  public addMessageHandler(type: string, handler: (data: any) => void) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, new Set());
    }
    this.messageHandlers.get(type)!.add(handler);
  }

  /**
   * Remove message handler
   */
  public removeMessageHandler(type: string, handler: (data: any) => void) {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.messageHandlers.delete(type);
      }
    }
  }

  /**
   * Add connection status handler
   */
  public addConnectionHandler(handler: (connected: boolean) => void) {
    this.connectionHandlers.add(handler);
  }

  /**
   * Remove connection status handler
   */
  public removeConnectionHandler(handler: (connected: boolean) => void) {
    this.connectionHandlers.delete(handler);
  }

  /**
   * Notify message handlers
   */
  private notifyHandlers(type: string, data: any) {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error('❌ Error in message handler:', error);
        }
      });
    }
  }

  /**
   * Get connection status
   */
  public isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * Close WebSocket connection
   */
  public disconnect() {
    this.shouldReconnect = false;
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    // Clear handlers
    this.messageHandlers.clear();
    this.connectionHandlers.clear();
  }

  /**
   * Reconnect manually
   */
  public reconnect() {
    this.shouldReconnect = true;
    this.disconnect();
    setTimeout(() => this.connect(), 1000);
  }
}

// Create singleton instance
const websocketService = new WebSocketService();

export default websocketService;
