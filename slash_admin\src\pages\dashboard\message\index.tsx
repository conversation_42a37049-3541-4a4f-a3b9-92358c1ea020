import React, { useState, useEffect } from 'react';
import {
  MessageSquare,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  Send,
  Inbox,
  TrendingUp,
  Phone,
  Calendar,
  BarChart3,
  FileText,
  Activity,
  Zap,
  Target,
  RefreshCw,
  Filter,
  ArrowUp,
  ArrowDown,
  Wifi,
  WifiOff
} from 'lucide-react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Avatar,
  Spin,
  Empty,
  Button,
  Space,
  Tooltip as AntTooltip,
  Badge,
  Typography,
  Divider,
  Select,
  DatePicker
} from 'antd';
import { 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as RechartsPieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  LineChart,
  Line
} from 'recharts';
import dayjs from 'dayjs';

import {
  UserGroupIcon,
  PlusIcon,
  Cog6ToothIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

import SendMessageModal from '../../messages/components/Messages/SendMessageModal';
import BulkMessageModal from '../../messages/components/Messages/BulkMessageModal';
import TaskManagementPanel from '../../messages/components/Tasks/TaskManagementPanel';
import messageService from '@/api/services/messageService';
import { toast } from 'sonner';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

interface MessageMetrics {
  totalMessages: number;
  inboundMessages: number;
  outboundMessages: number;
  unreadMessages: number;
  activeConversations: number;
  resolvedConversations: number;
  deliveryRate: number;
  averageResponseTime: string;
  topSMSCNumber: string;
  recentMessages: any[];
  messageTrends: any[];
  conversationsByStatus: Record<string, number>;
  messagesByDirection: Record<string, number>;
  hourlyDistribution: any[];
}

const SMSDashboard = () => {
  const [showSendMessage, setShowSendMessage] = useState(false);
  const [showBulkMessage, setShowBulkMessage] = useState(false);
  const [showTaskPanel, setShowTaskPanel] = useState(false);
  const [metrics, setMetrics] = useState<MessageMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [recentMessages, setRecentMessages] = useState<any[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [directionFilter, setDirectionFilter] = useState<string>('all');

  useEffect(() => {
    fetchDashboardData();
  }, [dateRange, directionFilter]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Build filter parameters
      const messageParams: any = {
        page_size: 200,
        ordering: '-created_at'
      };

      const conversationParams: any = {
        page_size: 200,
        ordering: '-last_message_at'
      };

      // Add date range filter
      if (dateRange) {
        messageParams.created_after = dateRange[0].format('YYYY-MM-DD');
        messageParams.created_before = dateRange[1].format('YYYY-MM-DD');
        conversationParams.created_after = dateRange[0].format('YYYY-MM-DD');
        conversationParams.created_before = dateRange[1].format('YYYY-MM-DD');
      }

      // Add direction filter
      if (directionFilter !== 'all') {
        messageParams.direction = directionFilter;
      }

      // Fetch messages data
      const messagesResponse = await messageService.getMessages(messageParams);

      // Fetch conversations data
      const conversationsResponse = await messageService.getConversations(conversationParams);
      
      const messages = messagesResponse.results || [];
      const conversations = conversationsResponse.results || [];
      
      // Calculate metrics from real data
      const totalMessages = messages.length;
      const inboundMessages = messages.filter(m => m.direction === 'inbound').length;
      const outboundMessages = messages.filter(m => m.direction === 'outbound').length;
      const unreadMessages = messages.filter(m => !m.is_read && m.direction === 'inbound').length;
      const activeConversations = conversations.filter(c => c.status === 'active').length;
      const resolvedConversations = conversations.filter(c => c.status === 'resolved').length;
      
      // Calculate delivery rate
      const outboundWithStatus = messages.filter(m => m.direction === 'outbound' && m.status);
      const deliveredMessages = outboundWithStatus.filter(m => m.status === 'delivered').length;
      const deliveryRate = outboundWithStatus.length > 0 ? (deliveredMessages / outboundWithStatus.length) * 100 : 100;
      
      // Calculate average response time
      const respondedConversations = conversations.filter(c => c.last_message_at && c.created_at);
      let averageResponseTime = '0m';
      if (respondedConversations.length > 0) {
        const totalMinutes = respondedConversations.reduce((sum, c) => {
          const created = dayjs(c.created_at);
          const lastMessage = dayjs(c.last_message_at);
          return sum + lastMessage.diff(created, 'minute');
        }, 0);
        const avgMinutes = totalMinutes / respondedConversations.length;
        const hours = Math.floor(avgMinutes / 60);
        const minutes = Math.floor(avgMinutes % 60);
        averageResponseTime = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
      }
      
      // Find top SMSC number
      const smscCounts = messages.reduce((acc, m) => {
        if (m.smsc_number) {
          acc[m.smsc_number] = (acc[m.smsc_number] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>);
      const topSMSCNumber = Object.entries(smscCounts).sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A';
      
      // Group conversations by status
      const conversationsByStatus = conversations.reduce((acc, c) => {
        acc[c.status] = (acc[c.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      // Group messages by direction
      const messagesByDirection = messages.reduce((acc, m) => {
        acc[m.direction] = (acc[m.direction] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      // Generate trend data (last 7 days)
      const messageTrends = [];
      for (let i = 6; i >= 0; i--) {
        const date = dayjs().subtract(i, 'day');
        const dayStart = date.startOf('day');
        const dayEnd = date.endOf('day');
        
        const dayMessages = messages.filter(m => {
          const createdAt = dayjs(m.created_at);
          return createdAt.isAfter(dayStart) && createdAt.isBefore(dayEnd);
        });

        messageTrends.push({
          date: date.format('MMM DD'),
          inbound: dayMessages.filter(m => m.direction === 'inbound').length,
          outbound: dayMessages.filter(m => m.direction === 'outbound').length,
          total: dayMessages.length
        });
      }
      
      // Generate hourly distribution
      const hourlyDistribution = [];
      for (let hour = 0; hour < 24; hour++) {
        const hourMessages = messages.filter(m => {
          const messageHour = dayjs(m.created_at).hour();
          return messageHour === hour;
        });
        
        hourlyDistribution.push({
          hour: `${hour}:00`,
          messages: hourMessages.length
        });
      }

      setMetrics({
        totalMessages,
        inboundMessages,
        outboundMessages,
        unreadMessages,
        activeConversations,
        resolvedConversations,
        deliveryRate,
        averageResponseTime,
        topSMSCNumber,
        recentMessages: messages.slice(0, 10),
        messageTrends,
        conversationsByStatus,
        messagesByDirection,
        hourlyDistribution
      });

      setRecentMessages(messages.slice(0, 10));
      
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
  };

  const clearFilters = () => {
    setDateRange(null);
    setDirectionFilter('all');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Spin size="large" />
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="flex items-center justify-center h-96">
        <Empty description="No data available" />
      </div>
    );
  }

  // Color schemes for charts
  const COLORS = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2'];
  
  const statusColors: Record<string, string> = {
    active: '#1890ff',
    resolved: '#52c41a',
    pending: '#faad14',
    closed: '#722ed1',
    spam: '#f5222d'
  };

  const directionColors: Record<string, string> = {
    inbound: '#1890ff',
    outbound: '#52c41a'
  };

  // Recent messages table columns
  const recentMessagesColumns = [
    {
      title: 'Direction',
      dataIndex: 'direction',
      key: 'direction',
      width: 100,
      render: (direction: string) => (
        <Tag color={directionColors[direction] || 'default'}>
          {direction?.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Content',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      render: (text: string) => (
        <span className="text-sm">{text?.substring(0, 50)}...</span>
      )
    },
    {
      title: 'Phone',
      dataIndex: 'source_addr',
      key: 'phone',
      width: 120,
      render: (phone: string, record: any) => (
        <span className="font-mono text-sm">
          {record.direction === 'inbound' ? record.source_addr : record.dest_addr}
        </span>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={status === 'delivered' ? 'green' : status === 'failed' ? 'red' : 'blue'}>
          {status?.toUpperCase() || 'PENDING'}
        </Tag>
      )
    },
    {
      title: 'Time',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date: string) => (
        <span className="text-sm text-gray-500">
          {dayjs(date).format('MMM DD, HH:mm')}
        </span>
      )
    }
  ];

  // Prepare chart data
  const conversationChartData = Object.entries(metrics.conversationsByStatus).map(([status, count]) => ({
    name: status.toUpperCase(),
    value: count,
    color: statusColors[status] || '#8884d8'
  }));

  const directionChartData = Object.entries(metrics.messagesByDirection).map(([direction, count]) => ({
    name: direction.toUpperCase(),
    value: count,
    color: directionColors[direction] || '#8884d8'
  }));

  return (
    <div className="p-6 bg-gradient-to-br from-green-50 to-emerald-100 min-h-screen">
      {/* Enhanced Header */}
      <Card className="mb-6 shadow-lg border-0">
        <Row justify="space-between" align="middle">
          <Col>
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg">
                <MessageSquare className="text-white" size={32} />
              </div>
              <div>
                <Title level={2} className="mb-1 text-gray-800">
                  Message Dashboard
                </Title>
                {/* <Text className="text-gray-600">
                  Monitor SMS communications and conversation metrics in real-time
                </Text> */}
              </div>
            </div>
          </Col>
          <Col>
            <Space size="middle">
              <AntTooltip title="Refresh Data">
                <Button
                  type="primary"
                  icon={refreshing ? <Spin size="small" /> : <RefreshCw size={16} />}
                  onClick={handleRefresh}
                  loading={refreshing}
                >
                  Refresh
                </Button>
              </AntTooltip>
              <AntTooltip title="Send Message">
                <Button
                  type="primary"
                  icon={<Send size={16} />}
                  onClick={() => setShowSendMessage(true)}
                  className="bg-green-500 hover:bg-green-600 border-green-500"
                >
                  Send Message
                </Button>
              </AntTooltip>
              <AntTooltip title="Bulk Message">
                <Button
                  icon={<UserGroupIcon className="w-4 h-4" />}
                  onClick={() => setShowBulkMessage(true)}
                >
                  Bulk Message
                </Button>
              </AntTooltip>
              <AntTooltip title="Task Management">
                <Button
                  icon={<Cog6ToothIcon className="w-4 h-4" />}
                  onClick={() => setShowTaskPanel(true)}
                >
                  Tasks
                </Button>
              </AntTooltip>
            </Space>
          </Col>
        </Row>

        <Divider />

        {/* Filters */}
        <Row gutter={16} align="middle">
          <Col>
            <Text strong className="text-gray-700">
              <Filter size={16} className="inline mr-2" />
              Filters:
            </Text>
          </Col>
          <Col>
            <Space>
              <RangePicker
                value={dateRange}
                onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)}
                placeholder={['Start Date', 'End Date']}
                allowClear
              />
              <Select
                value={directionFilter}
                onChange={setDirectionFilter}
                style={{ width: 120 }}
                placeholder="Direction"
              >
                <Select.Option value="all">All Messages</Select.Option>
                <Select.Option value="inbound">Inbound</Select.Option>
                <Select.Option value="outbound">Outbound</Select.Option>
              </Select>
              {(dateRange || directionFilter !== 'all') && (
                <Button size="small" onClick={clearFilters}>
                  Clear Filters
                </Button>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Enhanced Key Metrics Cards */}
      <Row gutter={[16, 16]} className="mb-6 mt-6">
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100"
            bodyStyle={{ padding: '24px' }}
          >
            <div className="flex items-center justify-center mb-3">
              <div className="p-3 bg-blue-500 rounded-full">
                <MessageSquare className="text-white" size={24} />
              </div>
            </div>
            <Statistic
              title={<Text className="text-gray-600 font-medium">Total Messages</Text>}
              value={metrics.totalMessages}
              valueStyle={{ color: '#1890ff', fontSize: '28px', fontWeight: 'bold' }}
            />
            <div className="mt-2">
              <Badge count={`+${Math.floor(metrics.totalMessages * 0.05)}`} showZero color="blue" />
              <Text className="text-xs text-gray-500 ml-2">vs last period</Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100"
            bodyStyle={{ padding: '24px' }}
          >
            <div className="flex items-center justify-center mb-3">
              <div className="p-3 bg-green-500 rounded-full">
                <Inbox className="text-white" size={24} />
              </div>
            </div>
            <Statistic
              title={<Text className="text-gray-600 font-medium">Inbound</Text>}
              value={metrics.inboundMessages}
              valueStyle={{ color: '#52c41a', fontSize: '28px', fontWeight: 'bold' }}
            />
            <div className="mt-2 flex items-center justify-center">
              <ArrowUp size={12} className="text-green-500 mr-1" />
              <Text className="text-xs text-green-600">Active incoming</Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100"
            bodyStyle={{ padding: '24px' }}
          >
            <div className="flex items-center justify-center mb-3">
              <div className="p-3 bg-orange-500 rounded-full">
                <Send className="text-white" size={24} />
              </div>
            </div>
            <Statistic
              title={<Text className="text-gray-600 font-medium">Outbound</Text>}
              value={metrics.outboundMessages}
              valueStyle={{ color: '#fa8c16', fontSize: '28px', fontWeight: 'bold' }}
            />
            {/* <div className="mt-2">
              <Progress
                percent={Math.round((metrics.outboundMessages / metrics.totalMessages) * 100)}
                showInfo={false}
                strokeColor="#fa8c16"
                size="small"
              />
            </div> */}
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-red-50 to-red-100"
            bodyStyle={{ padding: '24px' }}
          >
            <div className="flex items-center justify-center mb-3">
              <div className="p-3 bg-red-500 rounded-full">
                <AlertTriangle className="text-white" size={24} />
              </div>
            </div>
            <Statistic
              title={<Text className="text-gray-600 font-medium">Unread</Text>}
              value={metrics.unreadMessages}
              valueStyle={{ color: '#f5222d', fontSize: '28px', fontWeight: 'bold' }}
            />
            <div className="mt-2 flex items-center justify-center">
              {metrics.unreadMessages > 0 ? (
                <>
                  <AlertTriangle size={12} className="text-red-500 mr-1" />
                  <Text className="text-xs text-red-600">Needs attention</Text>
                </>
              ) : (
                <>
                  <CheckCircle size={12} className="text-green-500 mr-1" />
                  <Text className="text-xs text-green-600">All caught up!</Text>
                </>
              )}
            </div>
          </Card>
        </Col>
      </Row>

      {/* Secondary Metrics */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card className="text-center">
            <Statistic
              title="Active Conversations"
              value={metrics.activeConversations}
              prefix={<Users className="text-blue-500" size={20} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="text-center">
            <Statistic
              title="Resolved"
              value={metrics.resolvedConversations}
              prefix={<CheckCircle className="text-green-500" size={20} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="text-center">
            <Statistic
              title="Delivery Rate"
              value={metrics.deliveryRate}
              precision={2}
              suffix="%"
              prefix={<Target className="text-purple-500" size={20} />}
              valueStyle={{ color: metrics.deliveryRate >= 95 ? '#52c41a' : '#f5222d' }}
            />
            {/* <Progress 
              percent={metrics.deliveryRate} 
              showInfo={false} 
              strokeColor={metrics.deliveryRate >= 95 ? '#52c41a' : '#f5222d'}
              size="small"
            /> */}
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="text-center">
            <Statistic
              title="Avg Response Time"
              value={metrics.averageResponseTime}
              prefix={<Clock className="text-orange-500" size={20} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Charts Row */}
      <Row gutter={[16, 16]} className="mb-6">
        {/* Message Trends Chart */}
        <Col xs={24} lg={12}>
          <Card title="Message Trends (Last 7 Days)" extra={<BarChart3 size={16} />}>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={metrics.messageTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Area 
                  type="monotone" 
                  dataKey="inbound" 
                  stackId="1" 
                  stroke="#1890ff" 
                  fill="#1890ff" 
                  fillOpacity={0.6}
                  name="Inbound"
                />
                <Area 
                  type="monotone" 
                  dataKey="outbound" 
                  stackId="1" 
                  stroke="#52c41a" 
                  fill="#52c41a" 
                  fillOpacity={0.6}
                  name="Outbound"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* Conversation Status Distribution */}
        <Col xs={24} lg={12}>
          <Card title="Conversations by Status" extra={<Users size={16} />}>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <Pie
                  data={conversationChartData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {conversationChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </RechartsPieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* Message Direction and Hourly Distribution */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={12}>
          <Card title="Messages by Direction">
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={directionChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#1890ff">
                  {directionChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="Hourly Message Distribution">
            <ResponsiveContainer width="100%" height={250}>
              <LineChart data={metrics.hourlyDistribution}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="messages" 
                  stroke="#1890ff" 
                  strokeWidth={2}
                  dot={{ fill: '#1890ff' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* Recent Messages Table */}
      <Row>
        <Col span={24}>
          <Card title="Recent Messages" extra={<MessageSquare size={16} />}>
            <Table
              columns={recentMessagesColumns}
              dataSource={recentMessages}
              rowKey="id"
              pagination={false}
              size="small"
              scroll={{ x: 800 }}
            />
          </Card>
        </Col>
      </Row>

      {/* Modals */}
      <SendMessageModal
        isOpen={showSendMessage}
        onClose={() => setShowSendMessage(false)}
        onSend={async (messageData) => {
          try {
            await messageService.sendMessage(messageData);
            toast.success('Message sent successfully');
            setShowSendMessage(false);
            fetchDashboardData(); // Refresh data
          } catch (error) {
            toast.error('Failed to send message');
          }
        }}
      />

      <BulkMessageModal
        isOpen={showBulkMessage}
        onClose={() => setShowBulkMessage(false)}
        onSend={async (bulkData) => {
          try {
            await messageService.sendBulkMessage(bulkData);
            toast.success('Bulk messages sent successfully');
            setShowBulkMessage(false);
            fetchDashboardData(); // Refresh data
          } catch (error) {
            toast.error('Failed to send bulk messages');
          }
        }}
      />

      <TaskManagementPanel
        isOpen={showTaskPanel}
        onClose={() => setShowTaskPanel(false)}
      />
    </div>
  );
};

export default SMSDashboard;
