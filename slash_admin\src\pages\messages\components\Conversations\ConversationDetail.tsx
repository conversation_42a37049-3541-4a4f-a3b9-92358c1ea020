import React, { useState, useEffect, useRef } from 'react';
import { Conversation, Message, User } from '../../../../types';
import {
  UserIcon,
  PhoneIcon,
  MapPinIcon,
  ChatBubbleLeftIcon,
  ChatBubbleLeftEllipsisIcon,
  PaperAirplaneIcon,
  UserPlusIcon,
  CheckCircleIcon,
  TrashIcon,
  DocumentArrowDownIcon,
  EllipsisVerticalIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';
import messageService from '../../../../api/services/messageService';
import { toast } from 'sonner';
import CreateCaseModal from '../../../../components/modals/CreateCaseModal';
import useWebSocket from '../../../../hooks/useWebSocket';
import type { MessageData, ConversationUpdateData } from '../../../../services/websocketService';

interface ConversationDetailProps {
  conversation: Conversation;
}

export default function ConversationDetail({ conversation }: ConversationDetailProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [replyContent, setReplyContent] = useState('');
  const [sending, setSending] = useState(false);

  console.log('selcted conversation', conversation);

  // Context menu state
  const [contextMenu, setContextMenu] = useState<{
    show: boolean;
    x: number;
    y: number;
    messageId: string;
    messageDirection: string;
    messageContent: string;
  } | null>(null);

  // Edit message state
  const [editingMessage, setEditingMessage] = useState<{
    id: string;
    content: string;
  } | null>(null);

  // Create case modal state
  const [showCreateCaseModal, setShowCreateCaseModal] = useState(false);
  const [selectedMessageForCase, setSelectedMessageForCase] = useState<Message | null>(null);

  // Conversation actions state
  const [isDeleting, setIsDeleting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // WebSocket state
  const [isTyping, setIsTyping] = useState(false);
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('connecting');

  // Refs for scrolling
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // WebSocket integration
  const {
    isConnected,
    sendMessage: sendWebSocketMessage,
    sendTyping,
    subscribeToConversation,
    unsubscribeFromConversation,
    reconnect
  } = useWebSocket({
    conversationId: conversation.id,
    onNewMessage: (message: MessageData) => {
      console.log('🆕 New message received via WebSocket:', message);

      // IMPORTANT: Only process messages that belong to this conversation
      if (message.conversation_id !== conversation.id) {
        console.log(`🚫 Message belongs to conversation ${message.conversation_id}, current is ${conversation.id}. Ignoring.`);
        return;
      }

      console.log('✅ Message belongs to current conversation, adding to list');

      // Add new message to the list
      setMessages(prev => {
        // Check if message already exists to avoid duplicates
        const exists = prev.some(m => m.id === message.id);
        if (exists) {
          console.log('⚠️ Message already exists, skipping duplicate');
          return prev;
        }

        // Add new message and sort by created_at
        const newMessages = [...prev, message as any];
        return newMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      });

      // Scroll to bottom for new messages
      setTimeout(scrollToBottom, 100);

      // Show toast notification for inbound messages
      if (message.direction === 'inbound') {
        toast.success('New message received', {
          description: message.content.substring(0, 50) + (message.content.length > 50 ? '...' : ''),
          duration: 3000
        });
      }
    },
    onMessageUpdated: (message: MessageData) => {
      console.log('🔄 Message updated via WebSocket:', message);

      // IMPORTANT: Only process message updates that belong to this conversation
      if (message.conversation_id !== conversation.id) {
        console.log(`🚫 Message update belongs to conversation ${message.conversation_id}, current is ${conversation.id}. Ignoring.`);
        return;
      }

      console.log('✅ Message update belongs to current conversation, updating message');

      // Update existing message (e.g., status changes from 'sent' to 'delivered')
      setMessages(prev => prev.map(m => {
        if (m.id === message.id) {
          console.log(`📝 Updating message ${m.id} status from '${m.status}' to '${message.status}'`);
          return { ...m, ...message } as any;
        }
        return m;
      }));
    },
    onConversationUpdated: (update: ConversationUpdateData) => {
      console.log('🔄 Conversation updated via WebSocket:', update);
      // Handle conversation updates (unread count, status, etc.)
      // You might want to emit this to parent component or update local state
    },
    onConnectionChange: (connected: boolean) => {
      setConnectionStatus(connected ? 'connected' : 'disconnected');
      if (connected) {
        toast.success('Connected to real-time messaging', { duration: 2000 });
      } else {
        toast.error('Disconnected from real-time messaging', { duration: 3000 });
      }
    }
  });

  // Scroll to bottom function
  const scrollToBottom = (behavior: 'smooth' | 'auto' = 'smooth') => {
    messagesEndRef.current?.scrollIntoView({ behavior });
  };

  

  // Scroll to bottom on initial load and when messages update
  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages]);

  // Mark messages as read
  const markMessagesAsRead = async () => {
    try {
      console.log('________________Marking messages as read:', messages);
      // Mark all inbound messages in this conversation as read
      const unreadMessages = messages.filter(msg =>
        msg.direction === 'inbound' && !(msg as any).read_at
      );

      for (const message of unreadMessages) {
        console.log('Marking message as read:', message.id);
        await messageService.markMessageRead(message.id);
      }

      // Update local state
      setMessages(prev => prev.map(msg => ({
        ...msg,
        read_at: msg.direction === 'inbound' && !(msg as any).read_at ? new Date().toISOString() : (msg as any).read_at
      } as any)));

    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  };

  // Fetch conversation messages
  useEffect(() => {
    const fetchMessages = async () => {
      setLoading(true);
      try {
        console.log('Fetching messages for conversation:', conversation.id);
        const response = await messageService.getConversationMessages(conversation.id);
        console.log('Conversation messages response:', response);
        setMessages(response.results || response || []);

        // Mark messages as read after a short delay
        setTimeout(() => {
          markMessagesAsRead();
        }, 1000);

      } catch (error) {
        console.error('Error fetching conversation messages:', error);
        toast.error('Failed to load messages');
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();
  }, [conversation.id]);

  // Context menu handlers
  const handleRightClick = (e: React.MouseEvent, message: Message) => {
    e.preventDefault();
    setContextMenu({
      show: true,
      x: e.clientX,
      y: e.clientY,
      messageId: message.id,
      messageDirection: message.direction,
      messageContent: message.content
    });
  };

  const closeContextMenu = () => {
    setContextMenu(null);
  };

  // Edit message
  const handleEditMessage = () => {
    if (contextMenu) {
      setEditingMessage({
        id: contextMenu.messageId,
        content: contextMenu.messageContent
      });
      closeContextMenu();
    }
  };

  const saveEditedMessage = async () => {
    if (!editingMessage) return;

    try {
      await messageService.updateMessage(editingMessage.id, {
        content: editingMessage.content
      });

      // Update local state
      setMessages(prev => prev.map(msg =>
        msg.id === editingMessage.id
          ? { ...msg, content: editingMessage.content }
          : msg
      ));

      setEditingMessage(null);
      toast.success('Message updated successfully');
    } catch (error) {
      console.error('Error updating message:', error);
      toast.error('Failed to update message');
    }
  };

  const cancelEdit = () => {
    setEditingMessage(null);
  };

  // Delete message
  const handleDeleteMessage = async () => {
    if (!contextMenu) return;

    if (window.confirm('Are you sure you want to delete this message?')) {
      try {
        await messageService.deleteMessage(contextMenu.messageId);

        // Remove from local state
        setMessages(prev => prev.filter(msg => msg.id !== contextMenu.messageId));

        toast.success('Message deleted successfully');
      } catch (error) {
        console.error('Error deleting message:', error);
        toast.error('Failed to delete message');
      }
    }
    closeContextMenu();
  };

  // Create case from message
  const handleCreateCase = () => {
    if (!contextMenu) return;

    // Find the message object
    const message = messages.find(msg => msg.id === contextMenu.messageId);
    if (message) {
      setSelectedMessageForCase(message);
      setShowCreateCaseModal(true);
    }
    closeContextMenu();
  };

  // Handle case creation from modal
  const handleCaseSubmit = async (caseData: {
    title: string;
    description: string;
    status?: string;
    category?: string;
    priority?: string;
    tags?: string[];
    internal_notes?: string;
  }) => {
    if (!selectedMessageForCase) return;

    try {
      const response = await messageService.createCaseFromMessageTitle({
        title: caseData.title,
        description: caseData.description,
        category: caseData.category || 'general',
        priority: caseData.priority || 'medium',
        internal_notes: caseData.internal_notes || '',
        conversation: conversation.id,
        message: selectedMessageForCase.id
      });

      toast.success(`Case created successfully: ${response.case_id}`);
      setShowCreateCaseModal(false);
      setSelectedMessageForCase(null);
    } catch (error) {
      console.error('Error creating case:', error);
      throw error; // Let the modal handle the error
    }
  };

  // Send reply with WebSocket integration
  const handleSendReply = async () => {
    if (!replyContent.trim()) return;

    setSending(true);
    const messageContent = replyContent.trim();
    const destAddr = conversation.contact_phone || '';

    try {
      // Send via WebSocket first for real-time delivery
      if (isConnected && destAddr) {
        sendWebSocketMessage(messageContent, destAddr);
        console.log('📤 Message sent via WebSocket');
      }

      // Also send via HTTP API as fallback/backup
      await messageService.sendMessage({
        dest_addr: destAddr,
        content: messageContent,
        conversation_id: conversation.id
      });

      setReplyContent('');
      toast.success('Message sent successfully');

      // Stop typing indicator
      sendTyping(false);
      setIsTyping(false);

      // Clear typing timeout
      if (typingTimeout) {
        clearTimeout(typingTimeout);
        setTypingTimeout(null);
      }

      // Scroll to bottom after sending (WebSocket will handle message addition)
      setTimeout(scrollToBottom, 100);

    } catch (error) {
      console.error('Error sending reply:', error);
      toast.error('Failed to send message');

      // If WebSocket failed, try to refresh messages manually
      if (!isConnected) {
        try {
          const response = await messageService.getConversationMessages(conversation.id);
          setMessages(response.results || response || []);
          setTimeout(scrollToBottom, 100);
        } catch (refreshError) {
          console.error('Error refreshing messages:', refreshError);
        }
      }
    } finally {
      setSending(false);
    }
  };

  // Handle typing in reply input
  const handleReplyInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setReplyContent(value);

    // Send typing indicator
    if (isConnected && value.trim() && !isTyping) {
      setIsTyping(true);
      sendTyping(true);
    }

    // Clear existing timeout
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }

    // Set new timeout to stop typing indicator
    const timeout = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        sendTyping(false);
      }
    }, 2000); // Stop typing after 2 seconds of inactivity

    setTypingTimeout(timeout);

    // If input is empty, stop typing immediately
    if (!value.trim() && isTyping) {
      setIsTyping(false);
      sendTyping(false);
      clearTimeout(timeout);
      setTypingTimeout(null);
    }
  };

  // Assign conversation
  const handleAssign = async (userId: string) => {
    try {
      await messageService.assignConversation(conversation.id, userId);
      toast.success('Conversation assigned successfully');
    } catch (error) {
      console.error('Error assigning conversation:', error);
      toast.error('Failed to assign conversation');
    }
  };

  // Change status
  const handleStatusChange = async (status: string) => {
    try {
      await messageService.changeConversationStatus(conversation.id, status);
      toast.success('Status updated successfully');
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Failed to update status');
    }
  };

  const formatMessageTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered': return 'text-green-600';
      case 'enroute': return 'text-yellow-600';
      case 'undeliverable': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const handleDeleteConversation = async () => {
      setIsDeleting(true);
      try {
        await messageService.deleteConversation(conversation.id);
        toast.success('Conversation deleted successfully');
        // Navigate back or refresh parent component
        window.location.reload(); // Simple approach - you might want to use proper navigation
      } catch (error) {
        console.error('Error deleting conversation:', error);
        toast.error('Failed to delete conversation');
      } finally {
        setIsDeleting(false);
      }
    };
  
    // Export conversation as PDF
    const handleExportPDF = async () => {
      setIsExporting(true);
      try {
        // Validate that we have messages to export
        if (!messages || messages.length === 0) {
          toast.error('No messages to export. Please ensure the conversation has messages.');
          return;
        }

        // Create a new window for PDF generation
        const printWindow = window.open('', '_blank');
        if (!printWindow) {
          toast.error('Unable to open print window. Please allow popups and try again.');
          return;
        }

        // Generate HTML content for PDF
        const htmlContent = generatePDFContent();

        // Write content to the new window
        printWindow.document.write(htmlContent);
        printWindow.document.close();

        // Wait for content to load, then print
        printWindow.onload = () => {
          setTimeout(() => {
            try {
              printWindow.print();
              // Don't auto-close to let user save/print
              // printWindow.close();
            } catch (printError) {
              console.error('Print error:', printError);
              toast.error('Print dialog failed to open. Please try manually printing.');
            }
          }, 1000); // Increased timeout for better loading
        };

        toast.success('PDF export window opened. Use the print dialog to save as PDF.');
      } catch (error) {
        console.error('Error exporting conversation PDF:', error);
        toast.error(`Failed to export conversation: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setIsExporting(false);
      }
    };

    // Generate PDF content with chat-like styling
    const generatePDFContent = () => {
      const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        });
      };

      const formatTime = (dateString: string) => {
        return new Date(dateString).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        });
      };

      const conversationTitle = conversation.contact_name ||
                               conversation.contact?.name ||
                               conversation.contact_phone ||
                               'Unknown Contact';

      const messagesHtml = [...messages].reverse().map(message => {
        const isOutbound = message.direction === 'outbound';
        const messageTime = formatTime(message.created_at);

        // Escape HTML characters in message content
        const escapeHtml = (text: string) => {
          const div = document.createElement('div');
          div.textContent = text;
          return div.innerHTML;
        };

        const escapedContent = escapeHtml(message.content || '');

        return `
          <div class="message-container ${isOutbound ? 'outbound' : 'inbound'}">
            <div class="message-bubble ${isOutbound ? 'outbound-bubble' : 'inbound-bubble'}">
              <div class="message-content">${escapedContent}</div>
              <div class="message-meta">
                <span class="message-time">${messageTime}</span>
                ${isOutbound ? `<span class="message-status">${message.status || 'sent'}</span>` : ''}
              </div>
            </div>
          </div>
        `;
      }).join('');

      return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Conversation Export - ${conversationTitle}</title>
          <style>
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }

            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              background: #f8fafc;
              padding: 20px;
            }

            .header {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              padding: 30px;
              border-radius: 12px;
              margin-bottom: 30px;
              box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            }

            .header h1 {
              font-size: 28px;
              font-weight: 700;
              margin-bottom: 10px;
            }

            .header-info {
              display: flex;
              flex-wrap: wrap;
              gap: 20px;
              margin-top: 15px;
              font-size: 14px;
              opacity: 0.9;
            }

            .info-item {
              display: flex;
              align-items: center;
              gap: 8px;
            }

            .info-icon {
              width: 16px;
              height: 16px;
              opacity: 0.8;
            }

            .conversation-stats {
              background: white;
              padding: 20px;
              border-radius: 8px;
              margin-bottom: 30px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.05);
              border-left: 4px solid #667eea;
            }

            .stats-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
              gap: 20px;
            }

            .stat-item {
              text-align: center;
            }

            .stat-value {
              font-size: 24px;
              font-weight: 700;
              color: #667eea;
              display: block;
            }

            .stat-label {
              font-size: 12px;
              color: #6b7280;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              margin-top: 4px;
            }

            .messages-container {
              background: white;
              border-radius: 12px;
              padding: 30px;
              box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            }

            .messages-title {
              font-size: 20px;
              font-weight: 600;
              color: #1f2937;
              margin-bottom: 25px;
              padding-bottom: 15px;
              border-bottom: 2px solid #e5e7eb;
            }

            .message-container {
              margin-bottom: 20px;
              display: flex;
            }

            .message-container.outbound {
              justify-content: flex-end;
            }

            .message-container.inbound {
              justify-content: flex-start;
            }

            .message-bubble {
              max-width: 70%;
              padding: 12px 16px;
              border-radius: 18px;
              position: relative;
              word-wrap: break-word;
            }

            .outbound-bubble {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              border-bottom-right-radius: 4px;
            }

            .inbound-bubble {
              background: #f3f4f6;
              color: #1f2937;
              border-bottom-left-radius: 4px;
              border: 1px solid #e5e7eb;
            }

            .message-content {
              font-size: 14px;
              line-height: 1.5;
              margin-bottom: 6px;
            }

            .message-meta {
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-size: 11px;
              opacity: 0.8;
            }

            .outbound-bubble .message-meta {
              color: rgba(255,255,255,0.8);
            }

            .inbound-bubble .message-meta {
              color: #6b7280;
            }

            .message-time {
              font-weight: 500;
            }

            .message-status {
              text-transform: capitalize;
              font-weight: 500;
            }

            .footer {
              margin-top: 40px;
              text-align: center;
              padding: 20px;
              background: white;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            }

            .footer-text {
              color: #6b7280;
              font-size: 12px;
            }

            .export-date {
              font-weight: 600;
              color: #374151;
            }

            @media print {
              body {
                background: white;
                padding: 0;
              }

              .header, .conversation-stats, .messages-container, .footer {
                box-shadow: none;
                break-inside: avoid;
              }

              .message-container {
                break-inside: avoid;
              }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>📱 Conversation Export</h1>
            <div class="header-info">
              <div class="info-item">
                <span>👤</span>
                <span><strong>Contact:</strong> ${conversationTitle}</span>
              </div>
              <div class="info-item">
                <span>📞</span>
                <span><strong>Phone:</strong> ${conversation.contact_phone || 'N/A'}</span>
              </div>
              <div class="info-item">
                <span>📊</span>
                <span><strong>Status:</strong> ${conversation.status.toUpperCase()}</span>
              </div>
              <div class="info-item">
                <span>📅</span>
                <span><strong>Created:</strong> ${formatDate(conversation.created_at)}</span>
              </div>
            </div>
          </div>

          <div class="conversation-stats">
            <div class="stats-grid">
              <div class="stat-item">
                <span class="stat-value">${messages.length}</span>
                <span class="stat-label">Total Messages</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">${messages.filter(m => m.direction === 'inbound').length}</span>
                <span class="stat-label">Inbound</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">${messages.filter(m => m.direction === 'outbound').length}</span>
                <span class="stat-label">Outbound</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">${conversation.unread_count || 0}</span>
                <span class="stat-label">Unread</span>
              </div>
            </div>
          </div>

          <div class="messages-container">
            <h2 class="messages-title">💬 Conversation Messages</h2>
            ${messagesHtml}
          </div>

          <div class="footer">
            <p class="footer-text">
              Exported on <span class="export-date">${formatDate(new Date().toISOString())}</span>
              <br>
              Generated by SMS Management System
            </p>
          </div>
        </body>
        </html>
      `;
    };

    // Mark conversation as read
    const handleMarkAsRead = async () => {
      try {
        await messageService.markConversationRead(conversation.id);
        toast.success('Conversation marked as read');
        // Update local state
        setMessages(prev => prev.map(msg => ({
          ...msg,
          read_at: msg.direction === 'inbound' && !(msg as any).read_at ? new Date().toISOString() : (msg as any).read_at
        } as any)));
      } catch (error) {
        console.error('Error marking conversation as read:', error);
        toast.error('Failed to mark conversation as read');
      }
    };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b border-gray-200 bg-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                <UserIcon className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div>
              <h2 className="text-lg font-medium text-gray-900">
                {conversation.contact_name || conversation.contact?.name || 'Unknown Contact'}
              </h2>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <PhoneIcon className="h-4 w-4" />
                  <span>{conversation.contact_phone || 'Unknown Number'}</span>
                </div>
                {(conversation.contact?.location) && (
                  <div className="flex items-center space-x-1">
                    <MapPinIcon className="h-4 w-4" />
                    <span>{conversation.contact?.location}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Mark as Read Button */}
                        {conversation.unread_count > 0 && (
                          <button
                            onClick={handleMarkAsRead}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            title="Mark as Read"
                          >
                            <EyeIcon className="h-4 w-4 mr-1" />
                            Mark Read
                          </button>
                        )}
            
                        {/* Export PDF Button */}
                        <button
                          onClick={handleExportPDF}
                          disabled={isExporting}
                          className="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 transition-all duration-200"
                          title="Export conversation as PDF"
                        >
                          <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
                          {isExporting ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Generating PDF...
                            </>
                          ) : (
                            'Export PDF'
                          )}
                        </button>
            {/* Status dropdown */}
            <select
              value={conversation.status}
              onChange={(e) => handleStatusChange(e.target.value)}
              className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="active">Active</option>
              <option value="pending">Pending</option>
              <option value="resolved">Resolved</option>
              <option value="closed">Closed</option>
              <option value="spam">Spam</option>
            </select>

            {/* More Actions Dropdown */}
                        <div className="relative">
                          <button
                            onClick={() => {
                              // Toggle dropdown menu
                              const dropdown = document.getElementById('conversation-actions-dropdown');
                              if (dropdown) {
                                dropdown.classList.toggle('hidden');
                              }
                            }}
                            className="inline-flex items-center px-2 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            title="More Actions"
                          >
                            <EllipsisVerticalIcon className="h-4 w-4" />
                          </button>
            
                          {/* Dropdown Menu */}
                          <div
                            id="conversation-actions-dropdown"
                            className="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"
                          >
                            <div className="py-1">
                              <button
                                onClick={() => {
                                  if (window.confirm('Are you sure you want to delete this conversation? This action cannot be undone.')) {
                                    handleDeleteConversation();
                                  }
                                  document.getElementById('conversation-actions-dropdown')?.classList.add('hidden');
                                }}
                                disabled={isDeleting}
                                className="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50 disabled:opacity-50"
                              >
                                <TrashIcon className="h-4 w-4 mr-2" />
                                {isDeleting ? 'Deleting...' : 'Delete Conversation'}
                              </button>
                            </div>
                          </div>
                        </div>

            
          </div>
        </div>
      </div>

      {/* Messages */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-6 space-y-4 flex flex-col-reverse relative"
      >
        {loading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex space-x-3">
                  <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-16 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center py-8">
            <ChatBubbleLeftIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No messages</h3>
            <p className="mt-1 text-sm text-gray-500">
              This conversation has no messages yet.
            </p>
          </div>
        ) : (
          <>
            {/* Invisible element at the bottom for scrolling */}
            <div ref={messagesEndRef} />
            
            {/* Messages in reverse order (newest at bottom) */}
            {[...messages].reverse().map((message) => (
              <div
                key={message.id}
                className={`flex ${message.direction === 'outbound' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl cursor-pointer shadow-sm transition-shadow hover:shadow-md ${
                    message.direction === 'outbound'
                      ? 'bg-blue-600 text-white rounded-br-lg'
                      : 'bg-gray-100 text-gray-800 rounded-bl-lg'
                  }`}
                  onContextMenu={(e) => handleRightClick(e, message)}
                >
                  {/* Edit mode */}
                  {editingMessage?.id === message.id ? (
                    <div className="space-y-2">
                      <textarea
                        value={editingMessage.content}
                        onChange={(e) => setEditingMessage({
                          ...editingMessage,
                          content: e.target.value
                        })}
                        className="w-full text-sm bg-white text-gray-900 border border-gray-300 rounded px-2 py-1 resize-none"
                        rows={3}
                      />
                      <div className="flex space-x-2">
                        <button
                          onClick={saveEditedMessage}
                          className="px-2 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600"
                        >
                          Save
                        </button>
                        <button
                          onClick={cancelEdit}
                          className="px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="text-sm">{message.content}</div>
                      <div className={`text-xs mt-1 flex items-center justify-between ${
                        message.direction === 'outbound' ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        <span>{formatMessageTime(message.created_at)}</span>
                        {message.direction === 'outbound' && (
                          <span className={`ml-2 ${getStatusColor(message.status)}`}>
                            {message.status === 'delivered' && <CheckCircleIcon className="h-3 w-3" />}
                            {message.status}
                          </span>
                        )}
                      </div>
                    </>
                  )}
                </div>
              </div>
            ))}
          </>
        )}
      </div>

      {/* Reply input with WebSocket status */}
      <div className="border-t border-gray-200 bg-white px-6 py-4">
        {/* Connection Status Indicator */}
        {/* <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-500' :
              connectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
            }`}></div>
            <span className="text-xs text-gray-500">
              {connectionStatus === 'connected' ? '🟢 Real-time messaging active' :
               connectionStatus === 'connecting' ? '🟡 Connecting...' : '🔴 Offline mode'}
            </span>
            {connectionStatus === 'disconnected' && (
              <button
                onClick={reconnect}
                className="text-xs text-blue-600 hover:text-blue-800 underline"
              >
                Reconnect
              </button>
            )}
          </div>
          {isTyping && (
            <div className="flex items-center space-x-1 text-xs text-gray-500">
              <div className="flex space-x-1">
                <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span>Typing...</span>
            </div>
          )}
        </div> */}

        <div className="flex space-x-3">
          <div className="flex-1">
            <textarea
              value={replyContent}
              onChange={handleReplyInputChange}
              placeholder={isConnected ? "Type your reply... (real-time)" : "Type your reply... (offline mode)"}
              rows={3}
              className={`w-full border rounded-md px-3 py-2 text-sm focus:ring-2 focus:border-transparent resize-none transition-colors ${
                isConnected
                  ? 'border-green-300 focus:ring-green-500'
                  : 'border-gray-300 focus:ring-blue-500'
              }`}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendReply();
                }
              }}
            />
          </div>
          <button
            onClick={handleSendReply}
            disabled={!replyContent.trim() || sending}
            className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${
              isConnected
                ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
            }`}
          >
            <PaperAirplaneIcon className="h-4 w-4 mr-1" />
            {sending ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending...
              </>
            ) : (
              <>
                {isConnected ? '⚡ Send' : 'Send'}
              </>
            )}
          </button>
        </div>
        
        {/* Quick actions */}
        <div className="mt-3 flex space-x-2">
          <button
            onClick={() => handleStatusChange('resolved')}
            className="text-xs text-green-600 hover:text-green-800"
          >
            Mark Resolved
          </button>
          <button
            onClick={() => handleStatusChange('closed')}
            className="text-xs text-gray-600 hover:text-gray-800"
          >
            Close Conversation
          </button>
        </div>
      </div>

      {/* Context Menu */}
      {contextMenu && (
        <>
          {/* Backdrop to close menu */}
          <div
            className="fixed inset-0 z-40"
            onClick={closeContextMenu}
          />

          {/* Context Menu */}
          <div
            className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-[150px]"
            style={{
              left: contextMenu.x,
              top: contextMenu.y,
            }}
          >
            {/* Edit - only for outbound messages */}
            {contextMenu.messageDirection === 'outbound' && (
              <button
                onClick={handleEditMessage}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit
              </button>
            )}

            {/* Delete - only for outbound messages */}
            {contextMenu.messageDirection === 'outbound' && (
              <button
                onClick={handleDeleteMessage}
                className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Delete
              </button>
            )}

            {/* Create Case - only for inbound messages */}
            {contextMenu.messageDirection === 'inbound' && (
              <button
                onClick={handleCreateCase}
                className="w-full text-left px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Create Case
              </button>
            )}
          </div>
        </>
      )}

      {/* Create Case Modal */}
      <CreateCaseModal
        isOpen={showCreateCaseModal}
        onClose={() => {
          setShowCreateCaseModal(false);
          setSelectedMessageForCase(null);
        }}
        onSubmit={handleCaseSubmit}
        messageContent={selectedMessageForCase?.content}
      />
    </div>
  );
}
