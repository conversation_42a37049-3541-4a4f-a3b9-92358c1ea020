import React, { useState, useEffect } from 'react';
import { Case, Contact, Conversation } from '../../../../types';
import {
  ClipboardDocumentListIcon,
  UserIcon,
  PhoneIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  TagIcon,
  ChatBubbleLeftRightIcon,
  UserPlusIcon,
  CheckCircleIcon,
  XMarkIcon,
  MapPinIcon,
  LanguageIcon
} from '@heroicons/react/24/outline';
import messageService from '../../../../api/services/messageService';
import { toast } from 'sonner';

interface CaseDetailProps {
  case: Case;
}

export default function CaseDetail({ case: caseData }: CaseDetailProps) {
  const [resolutionNotes, setResolutionNotes] = useState(caseData.resolution_notes || '');
  const [updating, setUpdating] = useState(false);
  const [conversationData, setConversationData] = useState<Conversation | null>(null);
  const [contactData, setContactData] = useState<Contact | null>(null);
  const [loading, setLoading] = useState(true);

  console.log('Case datahhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh:', caseData);

  // Fetch conversation and contact data if conversation is just an ID
  useEffect(() => {
    const fetchConversationData = async () => {
      if (typeof caseData.conversation === 'string') {
        try {
          setLoading(true);
        const response = await messageService.getConversation(caseData.conversation);
        console.log('Conversation data lllllllllllllllll:', response);
        setConversationData(response);
        setContactData(response.contact);
        setLoading(false);
        } catch (error) {
          console.error('Error fetching conversation data:', error);
          setLoading(false);
        }
        // try {
        //   setLoading(true);
        //   // For now, we'll create a fallback contact from origin_message if available
        //   if (caseData.origin_message) {
        //     const fallbackContact: Contact = {
        //       id: 'fallback',
        //       name: 'Customer',
        //       phone_number: caseData.origin_message.source_addr || 'Unknown',
        //       preferred_language: 'en',
        //       opt_in_status: true,
        //       message_count: 1,
        //       case_count: 1,
        //       location: 'Unknown',
        //       created_at: caseData.created_at,
        //       updated_at: caseData.updated_at,
        //       ton: 1,
        //       npi: 1
        //     };
        //     setContactData(fallbackContact);
        //   }
        // } catch (error) {
        //   console.error('Error processing case data:', error);
        // } finally {
        //   setLoading(false);
        // }
      } else if (typeof caseData.conversation === 'object' && caseData.conversation.contact) {
        // Conversation is already a full object
        setConversationData(caseData.conversation);
        setContactData(caseData.conversation.contact);
        setLoading(false);
      } else {
        setLoading(false);
      }
    };

    fetchConversationData();
  }, [caseData.conversation, caseData.origin_message, caseData.created_at, caseData.updated_at]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'text-blue-600 bg-blue-100';
      case 'open': return 'text-green-600 bg-green-100';
      case 'in-progress': return 'text-yellow-600 bg-yellow-100';
      case 'resolved': return 'text-purple-600 bg-purple-100';
      case 'closed': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'power-outage': return 'text-red-600 bg-red-100';
      case 'bribery': return 'text-blue-600 bg-blue-100';
      // case 'telecom': return 'text-purple-600 bg-purple-100';
      case 'emergency': return 'text-red-600 bg-red-100';
      case 'billing': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const handleStatusChange = async (status: string) => {
    setUpdating(true);
    try {
      await messageService.changeCaseStatus(caseData.id, status);
      toast.success('Case status updated successfully');
    } catch (error) {
      console.error('Error updating case status:', error);
      toast.error('Failed to update case status');
    } finally {
      setUpdating(false);
    }
  };

  const handleAssign = async () => {
    // TODO: Show assign modal
    toast.info('Assignment modal not implemented yet');
  };

  console.log('Case data______________________________________________:', caseData);

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-6">
        {/* Header */}
        <div className="border-b border-gray-200 pb-6 mb-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <ClipboardDocumentListIcon className="h-6 w-6 text-gray-400" />
                <h1 className="text-xl font-semibold text-gray-900">
                  Case #{caseData.case_id}
                </h1>
                {caseData.sla_violated && (
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                )}
              </div>
              
              <h2 className="text-lg text-gray-700 mb-3">
                {caseData.title}
              </h2>
              
              <div className="flex items-center space-x-4 mb-4">
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(caseData.status)}`}>
                  {caseData.status}
                </span>
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getPriorityColor(caseData.priority)}`}>
                  {caseData.priority} priority
                </span>
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getCategoryColor(caseData.category)}`}>
                  {caseData.category}
                </span>
              </div>
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={handleAssign}
                disabled={updating}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                <UserPlusIcon className="h-4 w-4 mr-1" />
                Assign
              </button>
              
              {caseData.status !== 'resolved' && (
                <button
                  onClick={() => handleStatusChange('resolved')}
                  disabled={updating}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                >
                  <CheckCircleIcon className="h-4 w-4 mr-1" />
                  Resolve
                </button>
              )}
              
              {caseData.status !== 'closed' && (
                <button
                  onClick={() => handleStatusChange('closed')}
                  disabled={updating}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700 disabled:opacity-50"
                >
                  <XMarkIcon className="h-4 w-4 mr-1" />
                  Close
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Case details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Customer information */}
          <div className="bg-white shadow-lg rounded-xl p-6 border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
              <UserIcon className="h-5 w-5 text-blue-600" />
              <span>Customer Information</span>
            </h3>
            {loading ? (
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>
            ) : contactData ? (
              <dl className="space-y-4">
                <div className="flex items-start space-x-3">
                  <UserIcon className="h-4 w-4 text-gray-400 mt-1" />
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Name</dt>
                    <dd className="mt-1 text-sm font-semibold text-gray-900">{contactData.name}</dd>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <PhoneIcon className="h-4 w-4 text-gray-400 mt-1" />
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Phone Number</dt>
                    <dd className="mt-1 text-sm font-mono text-gray-900 bg-gray-50 px-2 py-1 rounded">
                      {contactData.phone_number}
                    </dd>
                  </div>
                </div>
                {contactData.location && (
                  <div className="flex items-start space-x-3">
                    <MapPinIcon className="h-4 w-4 text-gray-400 mt-1" />
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Location</dt>
                      <dd className="mt-1 text-sm text-gray-900">{contactData.location}</dd>
                    </div>
                  </div>
                )}
                <div className="flex items-start space-x-3">
                  <LanguageIcon className="h-4 w-4 text-gray-400 mt-1" />
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Language</dt>
                    <dd className="mt-1 text-sm text-gray-900 capitalize">
                      {contactData.preferred_language === 'am' ? 'Amharic (አማርኛ)' :
                       contactData.preferred_language === 'or' ? 'Oromo (Afaan Oromoo)' :
                       contactData.preferred_language === 'ti' ? 'Tigrinya (ትግርኛ)' :
                       contactData.preferred_language === 'en' ? 'English' :
                       contactData.preferred_language}
                    </dd>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className={`h-3 w-3 rounded-full ${contactData.opt_in_status ? 'bg-green-400' : 'bg-red-400'}`}></div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Status</dt>
                    <dd className={`mt-1 text-sm font-medium ${contactData.opt_in_status ? 'text-green-600' : 'text-red-600'}`}>
                      {contactData.opt_in_status ? 'Opted In' : 'Opted Out'}
                    </dd>
                  </div>
                </div>
              </dl>
            ) : (
              <div className="text-center py-4">
                <UserIcon className="mx-auto h-8 w-8 text-gray-400" />
                <p className="mt-2 text-sm text-gray-500">Customer information not available</p>
              </div>
            )}
          </div>

          {/* Case metadata */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Case Details</h3>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">Created</dt>
                <dd className="mt-1 text-sm text-gray-900 flex items-center space-x-1">
                  <ClockIcon className="h-4 w-4 text-gray-400" />
                  <span>{formatDate(caseData.created_at)}</span>
                </dd>
              </div>
              {caseData.assigned_to && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Assigned To</dt>
                  <dd className="mt-1 text-sm text-gray-900 flex items-center space-x-1">
                    <UserIcon className="h-4 w-4 text-gray-400" />
                    <span>{caseData.assigned_to.name || caseData.assigned_to.email || 'Unknown User'}</span>
                  </dd>
                </div>
              )}
              {caseData.sla_due_at && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">SLA Due</dt>
                  <dd className={`mt-1 text-sm ${
                    new Date(caseData.sla_due_at) < new Date() ? 'text-red-600' : 'text-gray-900'
                  }`}>
                    {formatDate(caseData.sla_due_at)}
                  </dd>
                </div>
              )}
              {caseData.tags && caseData.tags.length > 0 && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Tags</dt>
                  <dd className="mt-1">
                    <div className="flex flex-wrap gap-1">
                      {caseData.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </dd>
                </div>
              )}
            </dl>
          </div>
        </div>

        {/* Description */}
        <div className="bg-white shadow rounded-lg p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Description</h3>
          <p className="text-sm text-gray-700 whitespace-pre-wrap">
            {caseData.description}
          </p>
        </div>

        {/* Origin message */}
        {caseData.origin_message && (
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center space-x-2">
              <ChatBubbleLeftRightIcon className="h-5 w-5" />
              <span>Origin Message</span>
            </h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-sm text-gray-700 mb-2">
                {caseData.origin_message.content}
              </div>
              <div className="text-xs text-gray-500">
                Received {formatDate(caseData.origin_message.created_at)}
              </div>
            </div>
          </div>
        )}

        {/* Resolution notes */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Resolution Notes</h3>
          <textarea
            value={resolutionNotes}
            onChange={(e) => setResolutionNotes(e.target.value)}
            placeholder="Add resolution notes..."
            rows={4}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div className="mt-3 flex justify-end">
            <button
              onClick={() => {
                // TODO: Save resolution notes
                toast.success('Resolution notes saved');
              }}
              disabled={updating}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
            >
              Save Notes
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
