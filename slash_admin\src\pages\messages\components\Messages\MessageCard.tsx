import React from 'react';
import { format, isToday } from 'date-fns';
import { SMSMessage } from '../../../../types';
import StatusBadge from './StatusBadge';
import CategoryBadge from './CategoryBadge';
import PriorityBadge from './PriorityBadge';
import AssignmentStatusBadge from './AssignmentStatusBadge';
import ApprovalStatusBadge from './ApprovalStatusBadge';
import { UserIcon, ClockIcon, PhoneIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';

interface MessageCardProps {
  message: SMSMessage;
  isSelected: boolean;
  onClick: () => void;
}

export default function MessageCard({ message, isSelected, onClick }: MessageCardProps) {
  function formattedTime(timestamp: string | Date) {
  const date = new Date(timestamp);
  return isToday(date) ? format(date, 'h:mm a') : format(date, 'MMM d');
}

// console.log("MMMMMMMMMMMMMMMMMMMMMMM",message)

  return (
    <div
      className={clsx(
        'group p-4 rounded-xl border cursor-pointer transition-all duration-200 hover:shadow-md',
        isSelected
          ? 'border-blue-500 bg-blue-50 shadow-md ring-2 ring-blue-500/20'
          : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
      )}
      onClick={onClick}
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center gap-2">
          <div className="p-1.5 bg-gray-100 rounded-lg group-hover:bg-gray-200 transition-colors">
            <PhoneIcon className="h-4 w-4 text-gray-600" />
          </div>
          <div>
            <div className="font-semibold text-sm text-gray-900">{message.phone_number}</div>
            {/* <div className="text-xs text-gray-500">Case #{message.caseId}</div> */}
          </div>
        </div>
        <div className="text-right">
          <div className="text-xs text-gray-500 flex items-center gap-1">
            <ClockIcon className="h-3 w-3" />
            {formattedTime(
              message.timestamp
            )}
            {/* {formattedTime(
              message.replyCount > 0
                ? message.lastReply.timestamp
                : message.timestamp
            )} */}
          </div>
        </div>
      </div>

      {/* Status Badges */}
      {/* <div className="flex flex-wrap gap-1.5 mb-3">
        <StatusBadge status={message.status} />
        <CategoryBadge category={message.category} />
        <PriorityBadge priority={message.priority} />
      </div> */}

      {/* Assignment and Approval Status */}
      {(message.assigned_to || message.priority === 'critical') && (
        <div className="flex gap-2 mb-3">
          {message.assigned_to && (
            <AssignmentStatusBadge status="assigned" size="sm" />
          )}
          {message.priority === 'critical' && (
            <ApprovalStatusBadge status="pending" size="sm" />
          )}
        </div>
      )}

      {/* Message Content */}
      <p className="text-sm text-gray-700 line-clamp-2 mb-3 leading-relaxed">
        {message.latest_message.content}
        
        {/* {message.replyCount > 0 ? message.lastReply.content : message.content}  */}
        
      </p>

      {/* Assignment Info */}
      {message.assigned_to && (
        <div className="flex items-center gap-2 mb-3 p-2 bg-blue-50 rounded-lg">
          <UserIcon className="h-4 w-4 text-blue-600" />
          <div className="text-xs">
            <span className="text-gray-600">Assigned to </span>
            <span className="font-medium text-blue-700">{message.assigned_to.name}</span>
          </div>
        </div>
      )}

      {/* Tags */}
      {message.tags.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {message.tags.slice(0, 2).map((tag) => (
            <span
              key={tag}
              className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
            >
              {tag}
            </span>
          ))}
          {message.tags.length > 2 && (
            <span className="inline-flex items-center px-2 py-1 rounded-md text-xs text-gray-500 bg-gray-50">
              +{message.tags.length - 2} more
            </span>
          )}
        </div>
      )}

      {/* Unread Indicator */}
      {!isSelected && message.status === 'new' && (
        <div className="absolute top-3 right-3 w-2 h-2 bg-blue-500 rounded-full"></div>
      )}
    </div>
  );
}
