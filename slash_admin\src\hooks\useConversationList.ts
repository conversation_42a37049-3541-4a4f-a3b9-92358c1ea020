/**
 * React Hook for Real-Time Conversation List Updates
 * Manages conversation list with WebSocket real-time updates
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { Conversation } from '../types';
import messageService from '../api/services/messageService';
import { useWebSocket } from './useWebSocket';
import type { MessageData, ConversationUpdateData } from '../services/websocketService';
import { toast } from 'sonner';

export interface ConversationFilters {
  search?: string;
  status?: string;
  unread_only?: boolean;
  date_from?: string;
  date_to?: string;
}

export interface UseConversationListOptions {
  initialFilters?: ConversationFilters;
  itemsPerPage?: number;
  autoRefresh?: boolean;
}

export interface UseConversationListReturn {
  // Data
  conversations: Conversation[];
  loading: boolean;
  error: string | null;
  
  // Pagination
  currentPage: number;
  totalPages: number;
  totalCount: number;
  itemsPerPage: number;
  
  // Filters
  filters: ConversationFilters;
  
  // Actions
  setCurrentPage: (page: number) => void;
  setFilters: (filters: ConversationFilters) => void;
  refreshConversations: () => Promise<void>;
  
  // WebSocket status
  isConnected: boolean;
}

export const useConversationList = (options: UseConversationListOptions = {}): UseConversationListReturn => {
  const {
    initialFilters = {},
    itemsPerPage = 20,
    autoRefresh = true
  } = options;

  // State
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [filters, setFilters] = useState<ConversationFilters>(initialFilters);

  // Refs for stable references
  const conversationsRef = useRef<Conversation[]>([]);
  const filtersRef = useRef<ConversationFilters>(filters);
  const currentPageRef = useRef<number>(currentPage);

  // Update refs when state changes
  useEffect(() => {
    conversationsRef.current = conversations;
  }, [conversations]);

  useEffect(() => {
    filtersRef.current = filters;
  }, [filters]);

  useEffect(() => {
    currentPageRef.current = currentPage;
  }, [currentPage]);

  // Fetch conversations function
  const fetchConversations = useCallback(async (page: number = currentPageRef.current, currentFilters: ConversationFilters = filtersRef.current) => {
    setLoading(true);
    setError(null);

    try {
      const params = {
        page,
        page_size: itemsPerPage,
        ...currentFilters
      };

      const response = await messageService.getConversations(params);
      
      setConversations(response.results || []);
      setTotalPages(Math.ceil((response.count || 0) / itemsPerPage));
      setTotalCount(response.count || 0);
      
      console.log('📋 Conversations fetched:', response.results?.length || 0);
      
    } catch (err: any) {
      console.error('Error fetching conversations:', err);
      setError(err.message || 'Failed to fetch conversations');
      toast.error('Failed to fetch conversations');
    } finally {
      setLoading(false);
    }
  }, [itemsPerPage]);

  // Handle new message updates
  const handleNewMessage = useCallback((message: MessageData) => {
    console.log('🆕 New message for conversation list:', message);
    
    setConversations(prevConversations => {
      const updatedConversations = [...prevConversations];
      
      // Find the conversation this message belongs to
      const conversationIndex = updatedConversations.findIndex(
        conv => conv.id === message.conversation_id
      );
      
      if (conversationIndex >= 0) {
        // Update existing conversation
        const conversation = { ...updatedConversations[conversationIndex] };
        
        // Update last message info
        conversation.last_message_at = message.created_at;
        conversation.message_count = (conversation.message_count || 0) + 1;
        
        // Update unread count for inbound messages
        if (message.direction === 'inbound') {
          conversation.unread_count = (conversation.unread_count || 0) + 1;
        }
        
        // Update last message preview
        (conversation as any).last_message_preview = {
          content: message.content,
          direction: message.direction,
          created_at: message.created_at
        };
        
        // Move conversation to top of list
        updatedConversations.splice(conversationIndex, 1);
        updatedConversations.unshift(conversation);
        
        console.log('📝 Updated existing conversation:', conversation.id);
        
      } else {
        // This might be a new conversation - refresh the list
        console.log('🆕 Potential new conversation detected, refreshing list');
        fetchConversations(1, filtersRef.current);
      }
      
      return updatedConversations;
    });
    
    // Show toast notification for inbound messages
    if (message.direction === 'inbound') {
      const contactName = message.contact_name || message.source_addr;
      toast.success('New message received', {
        description: `From ${contactName}: ${message.content.substring(0, 50)}${message.content.length > 50 ? '...' : ''}`,
        duration: 4000
      });
    }
  }, [fetchConversations]);

  // Handle conversation updates
  const handleConversationUpdated = useCallback((update: ConversationUpdateData) => {
    console.log('🔄 Conversation updated:', update);
    
    setConversations(prevConversations => {
      return prevConversations.map(conv => {
        if (conv.id === update.conversation_id) {
          return {
            ...conv,
            ...update.updates,
            // Ensure we don't lose important fields
            id: conv.id,
            contact_name: update.updates.contact_name || conv.contact_name,
            contact_phone: update.updates.contact_phone || conv.contact_phone
          };
        }
        return conv;
      });
    });
  }, []);

  // WebSocket integration
  const { isConnected } = useWebSocket({
    onNewMessage: handleNewMessage,
    onConversationUpdated: handleConversationUpdated,
    onConnectionChange: (connected: boolean) => {
      if (connected) {
        console.log('✅ ConversationList connected to real-time updates');
        // Optionally refresh conversations when reconnected
        if (autoRefresh) {
          fetchConversations(currentPageRef.current, filtersRef.current);
        }
      } else {
        console.log('❌ ConversationList disconnected from real-time updates');
      }
    }
  });

  // Refresh conversations function
  const refreshConversations = useCallback(async () => {
    await fetchConversations(currentPageRef.current, filtersRef.current);
  }, [fetchConversations]);

  // Handle page changes
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
    fetchConversations(page, filtersRef.current);
  }, [fetchConversations]);

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: ConversationFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
    fetchConversations(1, newFilters);
  }, [fetchConversations]);

  // Initial fetch and when dependencies change
  useEffect(() => {
    fetchConversations(currentPage, filters);
  }, []); // Only run on mount

  // Fetch when page or filters change (handled by individual handlers)
  useEffect(() => {
    if (currentPage > 1) { // Don't refetch on initial mount
      fetchConversations(currentPage, filters);
    }
  }, [currentPage, fetchConversations]);

  return {
    // Data
    conversations,
    loading,
    error,
    
    // Pagination
    currentPage,
    totalPages,
    totalCount,
    itemsPerPage,
    
    // Filters
    filters,
    
    // Actions
    setCurrentPage: handlePageChange,
    setFilters: handleFiltersChange,
    refreshConversations,
    
    // WebSocket status
    isConnected
  };
};
