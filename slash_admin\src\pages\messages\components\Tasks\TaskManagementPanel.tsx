import React, { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { 
  XMarkIcon, 
  Cog6ToothIcon,
  PlayIcon,
  ArrowPathIcon,
  TrashIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';
import { toast } from 'sonner';

interface TaskManagementPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

interface TaskStatus {
  task_id: string;
  status: string;
  ready: boolean;
  successful?: boolean;
  failed?: boolean;
  result?: any;
  error?: string;
}

export default function TaskManagementPanel({ 
  isOpen, 
  onClose 
}: TaskManagementPanelProps) {
  const [celeryHealth, setCeleryHealth] = useState<any>(null);
  const [runningTasks, setRunningTasks] = useState<TaskStatus[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch Celery health status
  const fetchCeleryHealth = async () => {
    try {
      const response = await fetch('/api/celery-health/');
      if (response.ok) {
        const data = await response.json();
        setCeleryHealth(data);
      }
    } catch (error) {
      console.error('Error fetching Celery health:', error);
    }
  };

  // Execute task
  const executeTask = async (action: string, params: any = {}) => {
    setLoading(true);
    try {
      const response = await fetch('/api/tasks/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, ...params })
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(data.message);
        
        // Add to running tasks
        if (data.task_id) {
          setRunningTasks(prev => [...prev, {
            task_id: data.task_id,
            status: 'PENDING',
            ready: false
          }]);
          
          // Monitor task status
          monitorTask(data.task_id);
        }
      } else {
        const error = await response.json();
        toast.error(error.error || 'Task execution failed');
      }
    } catch (error) {
      console.error('Error executing task:', error);
      toast.error('Failed to execute task');
    } finally {
      setLoading(false);
    }
  };

  // Monitor task status
  const monitorTask = async (taskId: string) => {
    const checkStatus = async () => {
      try {
        const response = await fetch(`/api/tasks/${taskId}/status/`);
        if (response.ok) {
          const status = await response.json();
          
          setRunningTasks(prev => 
            prev.map(task => 
              task.task_id === taskId ? status : task
            )
          );
          
          // Continue monitoring if not ready
          if (!status.ready) {
            setTimeout(checkStatus, 2000);
          }
        }
      } catch (error) {
        console.error('Error checking task status:', error);
      }
    };

    checkStatus();
  };

  // Clear completed tasks
  const clearCompletedTasks = () => {
    setRunningTasks(prev => prev.filter(task => !task.ready));
  };

  useEffect(() => {
    if (isOpen) {
      fetchCeleryHealth();
    }
  }, [isOpen]);

  const taskActions = [
    {
      id: 'process_queue',
      name: 'Process Message Queue',
      description: 'Process pending outbound messages',
      icon: PlayIcon,
      color: 'blue'
    },
    {
      id: 'retry_failed',
      name: 'Retry Failed Messages',
      description: 'Retry messages that failed to send',
      icon: ArrowPathIcon,
      color: 'yellow'
    },
    {
      id: 'cleanup_logs',
      name: 'Cleanup Old Logs',
      description: 'Remove old SMPP logs and transactions',
      icon: TrashIcon,
      color: 'red'
    }
  ];

  const getTaskStatusIcon = (task: TaskStatus) => {
    if (!task.ready) {
      return <ClockIcon className="h-4 w-4 text-yellow-500 animate-spin" />;
    } else if (task.successful) {
      return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
    } else if (task.failed) {
      return <ExclamationCircleIcon className="h-4 w-4 text-red-500" />;
    }
    return <ClockIcon className="h-4 w-4 text-gray-500" />;
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900 flex items-center space-x-2">
                    <Cog6ToothIcon className="h-6 w-6" />
                    <span>Task Management</span>
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                {/* Celery health status */}
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Celery Status</h4>
                  {celeryHealth ? (
                    <div className="flex items-center space-x-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        celeryHealth.status === 'healthy' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {celeryHealth.status}
                      </span>
                      <span className="text-sm text-gray-600">
                        {celeryHealth.worker_count} worker(s) active
                      </span>
                      <button
                        onClick={fetchCeleryHealth}
                        className="text-xs text-blue-600 hover:text-blue-800"
                      >
                        Refresh
                      </button>
                    </div>
                  ) : (
                    <span className="text-sm text-gray-500">Loading...</span>
                  )}
                </div>

                {/* Task actions */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Available Tasks</h4>
                  <div className="grid grid-cols-1 gap-3">
                    {taskActions.map((action) => (
                      <button
                        key={action.id}
                        onClick={() => executeTask(action.id)}
                        disabled={loading}
                        className="flex items-center space-x-3 p-4 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed text-left"
                      >
                        <action.icon className={`h-5 w-5 text-${action.color}-600`} />
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {action.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {action.description}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Running tasks */}
                {runningTasks.length > 0 && (
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-sm font-medium text-gray-900">Running Tasks</h4>
                      <button
                        onClick={clearCompletedTasks}
                        className="text-xs text-gray-600 hover:text-gray-800"
                      >
                        Clear Completed
                      </button>
                    </div>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {runningTasks.map((task) => (
                        <div
                          key={task.task_id}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center space-x-3">
                            {getTaskStatusIcon(task)}
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {task.task_id.substring(0, 8)}...
                              </div>
                              <div className="text-xs text-gray-500">
                                Status: {task.status}
                              </div>
                            </div>
                          </div>
                          
                          {task.ready && (
                            <div className="text-xs">
                              {task.successful && task.result && (
                                <span className="text-green-600">{task.result}</span>
                              )}
                              {task.failed && task.error && (
                                <span className="text-red-600">{task.error}</span>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
