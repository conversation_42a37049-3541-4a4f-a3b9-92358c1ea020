import React from 'react';
import { Case } from '../../../../types';
import { 
  ClipboardDocumentListIcon, 
  ExclamationTriangleIcon,
  ClockIcon,
  UserIcon,
  TagIcon,
  PhoneIcon
} from '@heroicons/react/24/outline';

interface CaseListProps {
  cases: Case[];
  onSelectCase: (case_: Case) => void;
  selectedCaseId?: string;
  loading?: boolean;
}

export default function CaseList({ 
  cases, 
  onSelectCase, 
  selectedCaseId,
  loading 
}: CaseListProps) {

  console.log("CaseCCCCCCCCCCCCCC", cases)
  
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'text-blue-600 bg-blue-100';
      case 'open': return 'text-green-600 bg-green-100';
      case 'in-progress': return 'text-yellow-600 bg-yellow-100';
      case 'resolved': return 'text-purple-600 bg-purple-100';
      case 'closed': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'power-outage': return 'text-red-600 bg-red-100';
      case 'bribery': return 'text-blue-600 bg-blue-100';
      // case 'telecom': return 'text-purple-600 bg-purple-100';
      case 'emergency': return 'text-red-600 bg-red-100';
      case 'billing': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      return `${Math.floor(diffInHours * 60)}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  if (loading) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="bg-white p-4 rounded-lg shadow">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4">
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          Support Cases ({cases.length})
        </h2>
        
        {cases.length === 0 ? (
          <div className="text-center py-8">
            <ClipboardDocumentListIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No cases</h3>
            <p className="mt-1 text-sm text-gray-500">
              Support cases will appear here when created from messages.
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {cases.map((case_) => (
              <div
                key={case_.id}
                onClick={() => onSelectCase(case_)}
                className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                  selectedCaseId === case_.id
                    ? 'bg-blue-50 border-blue-200'
                    : 'bg-white border-gray-200 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    {/* Case header */}
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm font-medium text-gray-900">
                        #{case_.case_id}
                      </span>
                      {case_.priority === 'urgent' && (
                        <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />
                      )}
                      {case_.sla_violated && (
                        <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                          SLA Violated
                        </span>
                      )}
                    </div>
                    
                    {/* Title */}
                    <h3 className="text-sm font-medium text-gray-900 mb-2 truncate">
                      {case_.title}
                    </h3>
                    
                    {/* Description */}
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {case_.description}
                    </p>
                    
                    {/* Contact info */}
                    {/* <div className="flex items-center space-x-2 text-xs text-gray-500 mb-2">
                      <UserIcon className="h-3 w-3" />
                      <span>{case_.conversation?.contact?.name}</span>
                      <span>•</span>
                      <span>{case_.conversation?.contact?.phone_number}</span>
                    </div> */}

                    {case_.conversation_contact?.startsWith('Contact') ? (
                                            <div className="flex items-center space-x-2 mb-2">
                                              <div className="p-1.5 bg-gray-100 rounded-lg group-hover:bg-gray-200 transition-colors">
                                                <PhoneIcon className="h-4 w-4 text-gray-600" />
                                              </div>
                                              <div>
                                                <div className="font-semibold text-sm text-gray-900">
                                                  {case_.conversation_phone}
                                                </div>
                                                {/* <div className="text-xs text-gray-500">Case #{message.caseId}</div> */}
                                              </div>
                                            </div>
                                          ) : (
                                            <div className="flex items-center space-x-2 mb-2">
                                              <UserIcon className="h-4 w-4 text-gray-400" />
                                              <span className="text-sm font-medium text-gray-900 truncate">
                                                {case_.conversation_contact || 'Unknown Contact'}
                                              </span>
                                              <span className="text-xs text-gray-500">
                                                {case_.conversation_phone || 'Unknown Number'}
                                              </span>
                                            </div>
                                          )}
                    
                    {/* Tags */}
                    {case_.tags && case_.tags.length > 0 && (
                      <div className="flex items-center space-x-1 mb-2">
                        <TagIcon className="h-3 w-3 text-gray-400" />
                        <div className="flex space-x-1">
                          {/* {case_.tags.slice(0, 3).map((tag, index) => (
                            <span
                              key={index}
                              className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800"
                            >
                              {tag}
                            </span>
                          ))} */}
                          {case_.tags.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{case_.tags.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                    
                    {/* Timestamps */}
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <div className="flex items-center space-x-1">
                        <ClockIcon className="h-3 w-3" />
                        <span>Created {formatDate(case_.created_at)}</span>
                      </div>
                      {case_.sla_due_at && (
                        <span className={`${
                          new Date(case_.sla_due_at) < new Date() 
                            ? 'text-red-600' 
                            : 'text-gray-500'
                        }`}>
                          Due {formatDate(case_.sla_due_at)}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex flex-col items-end space-y-2">
                    {/* Status */}
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(case_.status)}`}>
                      {case_.status}
                    </span>
                    
                    {/* Priority */}
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(case_.priority)}`}>
                      {case_.priority}
                    </span>
                    
                    {/* Category */}
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(case_.category)}`}>
                      {case_.category}
                    </span>
                    
                    {/* Assignment */}
                    {/* {case_.assigned_to ? (
                      <span className="text-xs text-gray-500">
                        → {case_.assigned_to.first_name}
                      </span>
                    ) : (
                      <span className="text-xs text-gray-400">Unassigned</span>
                    )} */}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
