import React, { useRef, useState, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, MagnifyingGlassIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';
import { SMSMessage } from '../../../../types';
import MessageCard from './MessageCard';
import AdvancedFilters from './AdvancedFilters';
import { useMessages } from '../../../../hooks/useMessages';
import { useWebSocket } from '../../../../hooks/useWebSocket';
import type { MessageData } from '../../../../services/websocketService';
import { toast } from 'sonner';

interface MessageListProps {
  onSelectMessage: (message: SMSMessage) => void;
  selectedMessageId?: string;
}

export default function MessageList({ onSelectMessage, selectedMessageId }: MessageListProps) {
  const { messages, messagesList, loading, error, paginationInfo, fetchMessageContacts, fetchMessagesForContact } = useMessages();
  const [currentPage, setCurrentPage] = useState(1);
  const [messagesPerPage] = useState(20);
  const [filters, setFilters] = useState({
    status: 'all',
    category: 'all',
    priority: 'all',
    assignee: 'all',
    tags: [] as string[],
    dateRange: { start: '', end: '' },
    search: '',
    archived: false
  });
  const [filtersExpanded, setFiltersExpanded] = useState(false);

  // WebSocket integration for real-time updates
  const { isConnected } = useWebSocket({
    onNewMessage: (message: MessageData) => {
      console.log('🆕 New message received in MessageList:', message);

      // Show toast notification for new SMS messages
      if (message.direction === 'inbound') {
        toast.success('New SMS received', {
          description: `From ${message.source_addr}: ${message.content.substring(0, 50)}${message.content.length > 50 ? '...' : ''}`,
          duration: 4000
        });
      }

      // Refresh the message list to show the new message
      fetchMessageContacts();
    },
    onConnectionChange: (connected: boolean) => {
      if (connected) {
        console.log('✅ MessageList connected to real-time updates');
      } else {
        console.log('❌ MessageList disconnected from real-time updates');
      }
    }
  });

  // Handle MessageCard click - fetch detailed messages for the contact
  const handleContactClick = async (contact: SMSMessage) => {
    console.log('Contact clicked:', contact);

    try {
      // Fetch all messages for this contact's source_addr
      const contactMessages = await fetchMessagesForContact(contact.source_addr || contact.phone_number);

      if (contactMessages && contactMessages.length > 0) {
        // Pass the first message (or latest) to the parent for MessageDetail
        // The parent will use the selectedContactMessages from useMessages hook
        onSelectMessage(contactMessages[0]);
      } else {
        // If no detailed messages found, use the contact info as fallback
        onSelectMessage(contact);
      }
    } catch (error) {
      console.error('Error fetching contact messages:', error);
      // Fallback to using the contact info
      onSelectMessage(contact);
    }
  };

  const paginationRef = useRef(null);

  // Server-side pagination calculations
  const totalMessages = paginationInfo.count;
  const totalPages = paginationInfo.totalPages;
  const startIndex = (currentPage - 1) * messagesPerPage;
  const endIndex = Math.min(startIndex + messagesPerPage, totalMessages);

  // Fetch unique message contacts when filters or page changes (no repetition of source_addr)
  const fetchMessagesWithFilters = React.useCallback(() => {
    const params = {
      page: currentPage,
      page_size: messagesPerPage,
      ...(filters.status !== 'all' && { status: filters.status }),
      ...(filters.category !== 'all' && { category: filters.category }),
      ...(filters.priority !== 'all' && { priority: filters.priority }),
      ...(filters.assignee !== 'all' && {
        assigned_to: filters.assignee === 'unassigned' ? '' : filters.assignee
      }),
      ...(filters.tags.length > 0 && { tags: filters.tags.join(',') }),
      ...(filters.dateRange.start && { date_start: filters.dateRange.start }),
      ...(filters.dateRange.end && { date_end: filters.dateRange.end }),
      ...(filters.search && { search: filters.search }),
      archived: filters.archived,
      // ordering: '-timestamp'
    };

    console.log('Fetching message contacts with params:', params);
    fetchMessageContacts(params);
  }, [currentPage, messagesPerPage, filters.status, filters.category, filters.priority, filters.assignee, filters.tags, filters.dateRange.start, filters.dateRange.end, filters.search, filters.archived]); // Removed fetchMessages from dependencies

  // Debounced effect to prevent too many API calls
  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchMessagesWithFilters();
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [fetchMessagesWithFilters]);

  // Reset to first page when filters change (but not on initial load)
  React.useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [filters.status, filters.category, filters.priority, filters.assignee, filters.tags, filters.dateRange.start, filters.dateRange.end, filters.search, filters.archived]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePrevious = () => {
    setCurrentPage(prev => Math.max(prev - 1, 1));
  };

  const handleNext = () => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages));
  };



  return (
    <div className="h-full flex flex-col">

      <div className="p-6 border-b border-gray-200 bg-white">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <ChatBubbleLeftRightIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">Messages</h1>
              <p className="text-sm text-gray-500">{messages.length} total messages</p>
            </div>
          </div>
        </div>
      {/* Search Bar */}
      <div className=" bg-white">
      </div>

      {/* Advanced Filters */}
      <AdvancedFilters 
        filters={filters} 
        onFiltersChange={setFilters}
        isExpanded={filtersExpanded}
        totalMessages={totalMessages}
        onToggleExpanded={() => setFiltersExpanded(!filtersExpanded)}
      />

      {/* Messages List */}
      <div className="flex-1 overflow-y-auto">
        {totalMessages === 0 ? (
          <div className="p-8 text-center">
            <div className="mx-auto mb-4 p-3 bg-gray-100 rounded-full w-fit">
              <MagnifyingGlassIcon className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No messages found</h3>
            <p className="text-gray-500">Try adjusting your search terms or filters</p>
          </div>
        ) : loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-500">Loading messages...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <div className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit">
              <svg className="h-8 w-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading messages</h3>
            <p className="text-gray-500 mb-4">{error}</p>
            <button
              onClick={fetchMessagesWithFilters}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Retry
            </button>
          </div>
        ) : (
          <div className="space-y-1 p-2">
            {messagesList.map((message) => (
              <MessageCard
                key={message.id}
                message={message}
                isSelected={selectedMessageId === message.id}
                onClick={() => handleContactClick(message)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div ref={paginationRef} className="sticky bottom-0 border-t border-gray-200 px-4 py-3 bg-white z-10">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Showing {startIndex + 1}-{Math.min(endIndex, totalMessages)} of {totalMessages}
            </div>
            <div className="flex items-center gap-1">
              <button
                onClick={handlePrevious}
                disabled={currentPage === 1}
                className="p-2 rounded-lg border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <ChevronLeftIcon className="h-4 w-4" />
              </button>
              <div className="flex gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`min-w-[36px] h-9 px-3 rounded-lg text-sm font-medium transition-colors ${
                        currentPage === pageNum
                          ? 'bg-blue-600 text-white shadow-sm'
                          : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>
              <button
                onClick={handleNext}
                disabled={currentPage === totalPages}
                className="p-2 rounded-lg border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <ChevronRightIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
