// import React, { useEffect, useState } from 'react';
// import {
//   ChatBubbleLeftRightIcon,
//   InboxIcon,
//   UserGroupIcon,
//   FolderIcon,
//   ClipboardDocumentListIcon,
//   MagnifyingGlassIcon,
//   PlusIcon,
//   ArrowPathIcon,
//   Cog6ToothIcon,
//   WifiIcon,
//   ExclamationTriangleIcon
// } from '@heroicons/react/24/outline';
// import { Tab } from '@headlessui/react';
// import { toast } from 'sonner';

// // Import components
// import MessageList from './components/Messages/MessageList';
// import MessageDetail from './components/Messages/MessageDetail';
// import ConversationList from './components/Conversations/ConversationList';
// import ConversationDetail from './components/Conversations/ConversationDetail';
// // import CaseList from './components/Cases/CaseList';
// import CaseDetail from './components/Cases/CaseDetail';
// import ContactList from './components/Contacts/ContactList';
// import SendMessageModal from './components/Messages/SendMessageModal';
// import BulkMessageModal from './components/Messages/BulkMessageModal';
// import TaskManagementPanel from './components/Tasks/TaskManagementPanel';

// // Import types and hooks
// import { SMSMessage, Conversation, Case, Contact } from '../../types';
// import { useMessages } from '../../hooks/useMessages';
// import { useConversationList } from '../../hooks/useConversationList';
// import messageService from '../../api/services/messageService';

// function classNames(...classes: string[]) {
//   return classes.filter(Boolean).join(' ');
// }

// export default function Messages() {
//   // State management
//   const [activeTab, setActiveTab] = useState(0);
//   const [selectedMessage, setSelectedMessage] = useState<SMSMessage | null>(null);
//   const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
//   const [selectedCase, setSelectedCase] = useState<Case | null>(null);
//   const [selectedContact, setSelectedContact] = useState<Contact | null>(null);

//   // Modal states
//   const [showSendMessage, setShowSendMessage] = useState(false);
//   const [showBulkMessage, setShowBulkMessage] = useState(false);
//   const [showTaskPanel, setShowTaskPanel] = useState(false);

//   // Use real-time conversation list hook
//   const {
//     conversations,
//     loading: conversationsLoading,
//     error: conversationsError,
//     currentPage: conversationPage,
//     totalPages: conversationTotalPages,
//     totalCount: conversationTotalCount,
//     itemsPerPage,
//     setCurrentPage: setConversationPage,
//     isConnected: conversationsConnected
//   } = useConversationList({
//     itemsPerPage: 20,
//     autoRefresh: true
//   });

//   // Other data states
//   const [cases, setCases] = useState<Case[]>([]);
//   const [contacts, setContacts] = useState<Contact[]>([]);
//   const [loading, setLoading] = useState(false);
//   const [searchQuery, setSearchQuery] = useState('');
//   const [dashboardStats, setDashboardStats] = useState<any>(null);

//   // Other pagination states
//   const [casePage, setCasePage] = useState(1);
//   const [caseTotalPages, setCaseTotalPages] = useState(1);
//   const [contactPage, setContactPage] = useState(1);
//   const [contactTotalPages, setContactTotalPages] = useState(1);

//   // Use existing messages hook
//   const { messages, getReplies, fetchMessages } = useMessages();

//   // Tab configuration
//   const tabs = [
//     {
//       name: 'Messages',
//       icon: ChatBubbleLeftRightIcon,
//       count: messages.length,
//       description: 'Individual SMS messages'
//     },
//     {
//       name: 'Conversations',
//       icon: UserGroupIcon,
//       count: conversations.length,
//       description: 'Message threads with contacts'
//     },
//     {
//       name: 'Cases',
//       icon: ClipboardDocumentListIcon,
//       count: cases.length,
//       description: 'Support cases and tickets'
//     },
//     {
//       name: 'Contacts',
//       icon: FolderIcon,
//       count: contacts.length,
//       description: 'Customer contact database'
//     }
//   ];

//   // Fetch data based on active tab
//   useEffect(() => {
//     const fetchData = async () => {
//       setLoading(true);
//       try {
//         switch (activeTab) {
//           case 0: // Messages
//             await fetchMessages({ page: 1, page_size: 50 });
//             break;
//           case 1: // Conversations
//             // Conversations are now managed by useConversationList hook with real-time updates
//             break;
//           case 2: // Cases
//             const caseResponse = await messageService.getCases({ page: 1, page_size: 50 });
//             setCases(caseResponse.results || []);
//             break;
//           case 3: // Contacts
//             const contactResponse = await messageService.getContacts({ page: 1, page_size: 50 });
//             setContacts(contactResponse.results || []);
//             break;
//         }
//       } catch (error) {
//         console.error('Error fetching data:', error);
//         toast.error('Failed to fetch data');
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchData();
//   }, [activeTab, conversationPage, casePage, contactPage]);

//   // Fetch dashboard stats on mount
//   useEffect(() => {
//     const fetchStats = async () => {
//       try {
//         const stats = await messageService.getDashboardStats();
//         setDashboardStats(stats);
//       } catch (error) {
//         console.error('Error fetching dashboard stats:', error);
//       }
//     };

//     fetchStats();
//   }, []);

//   // Handle search
//   const handleSearch = async (query: string) => {
//     setSearchQuery(query);
//     if (query.trim()) {
//       try {
//         const results = await messageService.searchAll(query);
//         // Update relevant data based on active tab
//         switch (activeTab) {
//           case 0:
//             // Update messages with search results
//             break;
//           case 1:
//             setConversations(results.results?.conversations || []);
//             break;
//           case 2:
//             setCases(results.results?.cases || []);
//             break;
//           case 3:
//             setContacts(results.results?.contacts || []);
//             break;
//         }
//       } catch (error) {
//         console.error('Search error:', error);
//         toast.error('Search failed');
//       }
//     }
//   };

//   // Handle message selection and fetch replies
//   const handleSelectMessage = async (message: SMSMessage) => {
//     setSelectedMessage(message);
//     try {
//       const replies = await getReplies(message.id);
//       setSelectedMessage(prev => prev ? { ...prev, replies } : null);
//     } catch (err) {
//       console.error("Failed to fetch replies:", err);
//     }
//   };

//   return (
//     <div className="bg-white shadow-sm border-b border-gray-200 h-screen">
//       <Tab.Group selectedIndex={activeTab} onChange={setActiveTab}>
//         <Tab.List className="flex space-x-1 px-6">
//           {tabs.map((tab) => (
//             <Tab
//               key={tab.name}
//               className={({ selected }) =>
//                 classNames(
//                   'py-4 px-6 font-semibold text-sm focus:outline-none transition-all duration-200 rounded-t-xl border-b-4',
//                   selected
//                     ? 'border-blue-500 text-blue-600 bg-blue-50'
//                     : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
//                 )
//               }
//             >
//               <div className="flex items-center space-x-3">
//                 <tab.icon className="h-6 w-6" />
//                 <span className="text-base">{tab.name}</span>
//                 <span className={`py-1 px-3 rounded-full text-xs font-bold ${activeTab === tabs.indexOf(tab)
//                     ? 'bg-blue-100 text-blue-700'
//                     : 'bg-gray-100 text-gray-600'
//                   }`}>
//                   {tab.count}
//                 </span>
//               </div>
//             </Tab>
//           ))}
//         </Tab.List>

//         {/* Tab panels */}
//         <Tab.Panels className="flex-1 h-screen">
//           {/* Messages Tab */}
//           <Tab.Panel className="h-screen">
//             <div className="h-screen flex">
//               {/* Left side scrollable list */}
//               <div className="w-1/3 border-r border-gray-200 bg-gray-50 flex flex-col">
//                 <div className="flex-1 overflow-y-auto">
//                   <MessageList
//                     onSelectMessage={handleSelectMessage}
//                     selectedMessageId={selectedMessage?.id}
//                   />
//                 </div>
//               </div>

//               {/* Right side detail (non-scrollable sticky) */}
//               <div className="flex-1 flex flex-col">
//                 {selectedMessage ? (
//                   <MessageDetail message={selectedMessage} />
//                 ) : (
//                   <div className="flex-1 flex items-center justify-center">
//                     {/* empty state */}
//                   </div>
//                 )}
//               </div>
//             </div>
//           </Tab.Panel>

//           {/* Conversations Tab */}
//           <Tab.Panel className="">
//             <div className="h-screen flex">
//               <div className="w-1/3 border-r border-gray-200 bg-gray-50 overflow-hidden flex flex-col">
//                 <ConversationList
//                   conversations={conversations}
//                   onSelectConversation={setSelectedConversation}
//                   selectedConversationId={selectedConversation?.id}
//                   loading={conversationsLoading}
//                   currentPage={conversationPage}
//                   totalPages={conversationTotalPages}
//                   totalCount={conversationTotalCount}
//                   itemsPerPage={itemsPerPage}
//                   onPageChange={setConversationPage}
//                 />
//               </div>
//               <div className="flex-1 flex flex-col overflow-hidden bg-white">
//                 {selectedConversation ? (
//                   <ConversationDetail conversation={selectedConversation} />
//                 ) : (
//                   <div className="flex-1 flex items-center justify-center">
//                     <div className="text-center max-w-md">
//                       <div className="mx-auto mb-6 p-4 bg-gray-100 rounded-full w-fit">
//                         <ChatBubbleLeftRightIcon className="h-12 w-12 text-gray-400" />
//                       </div>
//                       <h3 className="text-xl font-medium text-gray-900 mb-2">
//                         Select a conversation
//                       </h3>
//                       <p className="text-gray-500">
//                         Choose a conversation to view the message thread and details.
//                       </p>
//                     </div>
//                   </div>
//                 )}
//               </div>
//             </div>
//           </Tab.Panel>

//           {/* Cases Tab */}
//           <Tab.Panel className="h-screen">
//             <div className="h-screen flex">
//               <div className="w-1/3 border-r border-gray-200 bg-gray-50">
//                 {/* <CaseList
//                   cases={cases}
//                   onSelectCase={setSelectedCase}
//                   selectedCaseId={selectedCase?.id}
//                   loading={loading}
//                 /> */}
//               </div>
//               <div className="flex-1 flex flex-col">
//                 {selectedCase ? (
//                   <CaseDetail case={selectedCase} />
//                 ) : (
//                   <div className="flex-1 flex items-center justify-center">
//                     <div className="text-center max-w-md">
//                       <div className="mx-auto mb-6 p-4 bg-gray-100 rounded-full w-fit">
//                         <ClipboardDocumentListIcon className="h-12 w-12 text-gray-400" />
//                       </div>
//                       <h3 className="text-xl font-medium text-gray-900 mb-2">
//                         Select a case
//                       </h3>
//                       <p className="text-gray-500">
//                         Choose a support case to view details and manage resolution.
//                       </p>
//                     </div>
//                   </div>
//                 )}
//               </div>
//             </div>
//           </Tab.Panel>

//           {/* Contacts Tab */}
//           <Tab.Panel className="h-screen">
//             <div className="h-screen flex">
//               <div className="w-1/3 border-r border-gray-200 bg-gray-50">
//                 <ContactList
//                   contacts={contacts}
//                   onSelectContact={setSelectedContact}
//                   selectedContactId={selectedContact?.id}
//                   loading={loading}
//                 />
//               </div>
//               <div className="flex-1 flex flex-col">
//                 {selectedContact ? (
//                   <div className="p-6">
//                     <h2 className="text-lg font-medium text-gray-900 mb-4">
//                       Contact Details
//                     </h2>
//                     <div className="bg-white shadow rounded-lg p-6">
//                       <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
//                         <div>
//                           <dt className="text-sm font-medium text-gray-500">Name</dt>
//                           <dd className="mt-1 text-sm text-gray-900">{selectedContact.name}</dd>
//                         </div>
//                         <div>
//                           <dt className="text-sm font-medium text-gray-500">Phone Number</dt>
//                           <dd className="mt-1 text-sm text-gray-900">{selectedContact.phone_number}</dd>
//                         </div>
//                         <div>
//                           <dt className="text-sm font-medium text-gray-500">Location</dt>
//                           <dd className="mt-1 text-sm text-gray-900">{selectedContact.location || 'N/A'}</dd>
//                         </div>
//                         <div>
//                           <dt className="text-sm font-medium text-gray-500">Language</dt>
//                           <dd className="mt-1 text-sm text-gray-900">{selectedContact.preferred_language}</dd>
//                         </div>
//                         <div>
//                           <dt className="text-sm font-medium text-gray-500">Opt-in Status</dt>
//                           <dd className="mt-1">
//                             <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${selectedContact.opt_in_status
//                                 ? 'bg-green-100 text-green-800'
//                                 : 'bg-red-100 text-red-800'
//                               }`}>
//                               {selectedContact.opt_in_status ? 'Opted In' : 'Opted Out'}
//                             </span>
//                           </dd>
//                         </div>
//                         <div>
//                           <dt className="text-sm font-medium text-gray-500">Message Count</dt>
//                           <dd className="mt-1 text-sm text-gray-900">{selectedContact.message_count}</dd>
//                         </div>
//                       </dl>
//                     </div>
//                   </div>
//                 ) : (
//                   <div className="flex-1 flex items-center justify-center">
//                     <div className="text-center max-w-md">
//                       <div className="mx-auto mb-6 p-4 bg-gray-100 rounded-full w-fit">
//                         <FolderIcon className="h-12 w-12 text-gray-400" />
//                       </div>
//                       <h3 className="text-xl font-medium text-gray-900 mb-2">
//                         Select a contact
//                       </h3>
//                       <p className="text-gray-500">
//                         Choose a contact to view their details and message history.
//                       </p>
//                     </div>
//                   </div>
//                 )}
//               </div>
//             </div>
//           </Tab.Panel>
//         </Tab.Panels>
//       </Tab.Group>
//     </div>
//   );
// }

import React from 'react'

function index() {
  return (
    <div>index</div>
  )
}

export default index