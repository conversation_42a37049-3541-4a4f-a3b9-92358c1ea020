import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Clock,
  CheckCircle,
  FileText,
  TrendingUp,
  Users,
  Calendar,
  Target,
  Activity,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  RefreshCw,
  Filter,
  Download,
  Eye,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Avatar,
  Spin,
  Empty,
  Button,
  Space,
  Tooltip as AntTooltip,
  Badge,
  Typography,
  Divider,
  Select,
  DatePicker
} from 'antd';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as <PERSON>charts<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Line<PERSON><PERSON>,
  Line,
  Legend
} from 'recharts';
import dayjs from 'dayjs';
import messageService from '../../../api/services/messageService';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

interface DashboardMetrics {
  totalCases: number;
  newCases: number;
  inProgressCases: number;
  resolvedCases: number;
  closedCases: number;
  highPriorityCases: number;
  overdueCase: number;
  averageResolutionTime: string;
  casesByStatus: Record<string, number>;
  casesByPriority: Record<string, number>;
  casesByCategory: Record<string, number>;
  recentCases: any[];
  caseTrends: any[];
  slaCompliance: number;
}

const Dashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [recentCases, setRecentCases] = useState<any[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');

  useEffect(() => {
    fetchDashboardData();
  }, [dateRange, statusFilter, priorityFilter]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Build filter parameters
      const filterParams: any = {
        page_size: 200,
        ordering: '-created_at'
      };

      // Add date range filter
      if (dateRange) {
        filterParams.created_after = dateRange[0].format('YYYY-MM-DD');
        filterParams.created_before = dateRange[1].format('YYYY-MM-DD');
      }

      // Add status filter
      if (statusFilter !== 'all') {
        filterParams.status = statusFilter;
      }

      // Add priority filter
      if (priorityFilter !== 'all') {
        filterParams.priority = priorityFilter;
      }

      // Fetch cases data
      const casesResponse = await messageService.getCases(filterParams);
      
      const cases = casesResponse.results || [];
      
      // Calculate metrics from real data
      const totalCases = cases.length;
      const newCases = cases.filter(c => c.status === 'new').length;
      const inProgressCases = cases.filter(c => c.status === 'in_progress').length;
      const resolvedCases = cases.filter(c => c.status === 'resolved').length;
      const closedCases = cases.filter(c => c.status === 'closed').length;
      const highPriorityCases = cases.filter(c => c.priority === 'high').length;
      const overdueCase = cases.filter(c => {
        if (!c.sla_due_at) return false;
        return dayjs().isAfter(dayjs(c.sla_due_at)) && !['resolved', 'closed'].includes(c.status);
      }).length;

      // Calculate SLA compliance
      const casesWithSLA = cases.filter(c => c.sla_due_at);
      const slaCompliantCases = casesWithSLA.filter(c => {
        if (['resolved', 'closed'].includes(c.status)) {
          const resolvedAt = c.resolved_at || c.closed_at;
          return resolvedAt && dayjs(resolvedAt).isBefore(dayjs(c.sla_due_at));
        }
        return dayjs().isBefore(dayjs(c.sla_due_at));
      });
      const slaCompliance = casesWithSLA.length > 0 ? (slaCompliantCases.length / casesWithSLA.length) * 100 : 100;

      // Group by status
      const casesByStatus = cases.reduce((acc, c) => {
        acc[c.status] = (acc[c.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Group by priority
      const casesByPriority = cases.reduce((acc, c) => {
        acc[c.priority] = (acc[c.priority] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Group by category
      const casesByCategory = cases.reduce((acc, c) => {
        acc[c.category] = (acc[c.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Calculate average resolution time
      const resolvedCasesWithTime = cases.filter(c => 
        c.status === 'resolved' && c.created_at && c.resolved_at
      );
      let averageResolutionTime = '0h';
      if (resolvedCasesWithTime.length > 0) {
        const totalMinutes = resolvedCasesWithTime.reduce((sum, c) => {
          const created = dayjs(c.created_at);
          const resolved = dayjs(c.resolved_at);
          return sum + resolved.diff(created, 'minute');
        }, 0);
        const avgMinutes = totalMinutes / resolvedCasesWithTime.length;
        const hours = Math.floor(avgMinutes / 60);
        const minutes = Math.floor(avgMinutes % 60);
        averageResolutionTime = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
      }

      // Generate trend data (last 7 days)
      const caseTrends = [];
      for (let i = 6; i >= 0; i--) {
        const date = dayjs().subtract(i, 'day');
        const dayStart = date.startOf('day');
        const dayEnd = date.endOf('day');
        
        const dayCases = cases.filter(c => {
          const createdAt = dayjs(c.created_at);
          return createdAt.isAfter(dayStart) && createdAt.isBefore(dayEnd);
        });

        caseTrends.push({
          date: date.format('MMM DD'),
          new: dayCases.filter(c => c.status === 'new').length,
          resolved: dayCases.filter(c => c.status === 'resolved').length,
          total: dayCases.length
        });
      }

      setMetrics({
        totalCases,
        newCases,
        inProgressCases,
        resolvedCases,
        closedCases,
        highPriorityCases,
        overdueCase,
        averageResolutionTime,
        casesByStatus,
        casesByPriority,
        casesByCategory,
        recentCases: cases.slice(0, 10),
        caseTrends,
        slaCompliance
      });

      setRecentCases(cases.slice(0, 10));
      
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
  };

  const handleExportData = () => {
    // Navigate to cases page with export functionality
    window.location.href = '/case';
  };

  const clearFilters = () => {
    setDateRange(null);
    setStatusFilter('all');
    setPriorityFilter('all');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Spin size="large" />
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="flex items-center justify-center h-96">
        <Empty description="No data available" />
      </div>
    );
  }

  // Color schemes for charts
  const COLORS = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2'];
  
  const statusColors: Record<string, string> = {
    new: '#1890ff',
    in_progress: '#faad14', 
    resolved: '#52c41a',
    closed: '#722ed1',
    cancelled: '#f5222d'
  };

  const priorityColors: Record<string, string> = {
    low: '#52c41a',
    medium: '#faad14',
    high: '#f5222d',
    urgent: '#722ed1'
  };

  // Recent cases table columns
  const recentCasesColumns = [
    {
      title: 'Case ID',
      dataIndex: 'case_id',
      key: 'case_id',
      width: 120,
      render: (text: string) => (
        <span className="font-mono text-sm">{text}</span>
      )
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      render: (text: string) => (
        <span className="font-medium">{text}</span>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={statusColors[status] || 'default'}>
          {status?.replace('_', ' ').toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority: string) => (
        <Tag color={priorityColors[priority] || 'default'}>
          {priority?.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date: string) => (
        <span className="text-sm text-gray-500">
          {dayjs(date).format('MMM DD, HH:mm')}
        </span>
      )
    }
  ];

  // Prepare chart data
  const statusChartData = Object.entries(metrics.casesByStatus).map(([status, count]) => ({
    name: status.replace('_', ' ').toUpperCase(),
    value: count,
    color: statusColors[status] || '#8884d8'
  }));

  const priorityChartData = Object.entries(metrics.casesByPriority).map(([priority, count]) => ({
    name: priority.toUpperCase(),
    value: count,
    color: priorityColors[priority] || '#8884d8'
  }));

  const categoryChartData = Object.entries(metrics.casesByCategory).map(([category, count], index) => ({
    name: category.replace('_', ' ').toUpperCase(),
    value: count,
    color: COLORS[index % COLORS.length]
  }));

  return (
    <div className="p-6 bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
      {/* Enhanced Header */}
      <Card className="mb-6 shadow-lg border-0">
        <Row justify="space-between" align="middle">
          <Col>
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg">
                <BarChart3 className="text-white" size={32} />
              </div>
              <div>
                <Title level={2} className="mb-1 text-gray-800">
                  Case Management Dashboard
                </Title>
                {/* <Text className="text-gray-600">
                  Monitor and track case performance metrics in real-time
                </Text> */}
              </div>
            </div>
          </Col>
          <Col>
            <Space size="middle">
              <AntTooltip title="Refresh Data">
                <Button
                  type="primary"
                  icon={refreshing ? <Spin size="small" /> : <RefreshCw size={16} />}
                  onClick={handleRefresh}
                  loading={refreshing}
                >
                  Refresh
                </Button>
              </AntTooltip>
              {/* <AntTooltip title="Export Cases">
                <Button
                  icon={<Download size={16} />}
                  onClick={handleExportData}
                >
                  Export
                </Button>
              </AntTooltip> */}
              <AntTooltip title="View All Cases">
                <Button
                  icon={<Eye size={16} />}
                  onClick={() => window.location.href = '/#/case'}
                >
                  View All
                </Button>
              </AntTooltip>
            </Space>
          </Col>
        </Row>

        <Divider />

        {/* Filters */}
        <Row gutter={16} align="middle">
          <Col>
            <Text strong className="text-gray-700">
              <Filter size={16} className="inline mr-2" />
              Filters:
            </Text>
          </Col>
          <Col>
            <Space>
              <RangePicker
                value={dateRange}
                onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)}
                placeholder={['Start Date', 'End Date']}
                allowClear
              />
              <Select
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: 120 }}
                placeholder="Status"
              >
                <Select.Option value="all">All Status</Select.Option>
                <Select.Option value="new">New</Select.Option>
                <Select.Option value="in_progress">In Progress</Select.Option>
                <Select.Option value="resolved">Resolved</Select.Option>
                <Select.Option value="closed">Closed</Select.Option>
              </Select>
              <Select
                value={priorityFilter}
                onChange={setPriorityFilter}
                style={{ width: 120 }}
                placeholder="Priority"
              >
                <Select.Option value="all">All Priority</Select.Option>
                <Select.Option value="low">Low</Select.Option>
                <Select.Option value="medium">Medium</Select.Option>
                <Select.Option value="high">High</Select.Option>
                <Select.Option value="urgent">Urgent</Select.Option>
              </Select>
              {(dateRange || statusFilter !== 'all' || priorityFilter !== 'all') && (
                <Button size="small" onClick={clearFilters}>
                  Clear Filters
                </Button>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Enhanced Key Metrics Cards */}
      <Row gutter={[16, 16]} className="mb-6 mt-6">
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100"
            bodyStyle={{ padding: '24px' }}
          >
            <div className="flex items-center justify-center mb-3">
              <div className="p-3 bg-blue-500 rounded-full">
                <FileText className="text-white" size={24} />
              </div>
            </div>
            <Statistic
              title={<Text className="text-gray-600 font-medium">Total Cases</Text>}
              value={metrics.totalCases}
              valueStyle={{ color: '#1890ff', fontSize: '28px', fontWeight: 'bold' }}
            />
            <div className="mt-2">
              <Badge count={`+${Math.floor(metrics.totalCases * 0.1)}`} showZero color="blue" />
              <Text className="text-xs text-gray-500 ml-2">vs last period</Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100"
            bodyStyle={{ padding: '24px' }}
          >
            <div className="flex items-center justify-center mb-3">
              <div className="p-3 bg-orange-500 rounded-full">
                <AlertTriangle className="text-white" size={24} />
              </div>
            </div>
            <Statistic
              title={<Text className="text-gray-600 font-medium">New Cases</Text>}
              value={metrics.newCases}
              valueStyle={{ color: '#fa8c16', fontSize: '28px', fontWeight: 'bold' }}
            />
            <div className="mt-2 flex items-center justify-center">
              <ArrowUp size={12} className="text-orange-500 mr-1" />
              <Text className="text-xs text-orange-600">Needs attention</Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-yellow-50 to-yellow-100"
            bodyStyle={{ padding: '24px' }}
          >
            <div className="flex items-center justify-center mb-3">
              <div className="p-3 bg-yellow-500 rounded-full">
                <Clock className="text-white" size={24} />
              </div>
            </div>
            <Statistic
              title={<Text className="text-gray-600 font-medium">In Progress</Text>}
              value={metrics.inProgressCases}
              valueStyle={{ color: '#faad14', fontSize: '28px', fontWeight: 'bold' }}
            />
            {/* <div className="mt-2">
              <Progress
                percent={Math.round((metrics.inProgressCases / metrics.totalCases) * 100)}
                showInfo={false}
                strokeColor="#faad14"
                size="small"
              />
            </div> */}
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100"
            bodyStyle={{ padding: '24px' }}
          >
            <div className="flex items-center justify-center mb-3">
              <div className="p-3 bg-green-500 rounded-full">
                <CheckCircle className="text-white" size={24} />
              </div>
            </div>
            <Statistic
              title={<Text className="text-gray-600 font-medium">Resolved</Text>}
              value={metrics.resolvedCases}
              valueStyle={{ color: '#52c41a', fontSize: '28px', fontWeight: 'bold' }}
            />
            <div className="mt-2 flex items-center justify-center">
              <ArrowUp size={12} className="text-green-500 mr-1" />
              <Text className="text-xs text-green-600">Great progress!</Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Enhanced Secondary Metrics */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-red-50 to-red-100"
            bodyStyle={{ padding: '20px' }}
          >

            <div className="flex items-center justify-center mb-3">
              <div className="p-2 bg-red-500 rounded-lg">
                <Target className="text-white" size={20} />
              </div>
            </div>
            {/* <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-red-500 rounded-lg">
                <Target className="text-white" size={20} />
              </div>
              {metrics.highPriorityCases > 0 && (
                <Badge count={metrics.highPriorityCases} color="red" />
              )}
            </div> */}
            <Statistic
              title={<Text className="text-gray-600 font-medium">High Priority</Text>}
              value={metrics.highPriorityCases}
              valueStyle={{ color: '#f5222d', fontSize: '24px', fontWeight: 'bold' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-red-50 to-pink-100"
            bodyStyle={{ padding: '20px' }}
          >
            <div className="flex items-center justify-center mb-3">
              <div className="p-2 bg-red-500 rounded-lg">
                <Calendar className="text-white" size={20} />
              </div>
            </div>
            {/* <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-red-500 rounded-lg">
                <Calendar className="text-white" size={20} />
              </div>
              {metrics.overdueCase > 0 && (
                <Badge count="!" color="red" />
              )}
            </div> */}
            <Statistic
              title={<Text className="text-gray-600 font-medium">Overdue</Text>}
              value={metrics.overdueCase}
              valueStyle={{ color: '#f5222d', fontSize: '24px', fontWeight: 'bold' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card
            className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100"
            bodyStyle={{ padding: '20px' }}
          >
            <div className="flex items-center justify-center mb-3">
              <div className="p-2 bg-purple-500 rounded-lg">
                <Activity className="text-white" size={20} />
              </div>
            </div>
            <Statistic
              title={<Text className="text-gray-600 font-medium">Avg Resolution</Text>}
              value={metrics.averageResolutionTime}
              valueStyle={{ color: '#722ed1', fontSize: '24px', fontWeight: 'bold' }}
            />
          </Card>
        </Col>
        {/* <Col xs={24} sm={12} lg={6}>
          <Card
            className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-emerald-100"
            bodyStyle={{ padding: '20px' }}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="p-2 bg-green-500 rounded-lg">
                <TrendingUp className="text-white" size={20} />
              </div>
              <Badge
                count={metrics.slaCompliance >= 90 ? "✓" : "!"}
                color={metrics.slaCompliance >= 90 ? "green" : "red"}
              />
            </div>
            <Statistic
              title={<Text className="text-gray-600 font-medium">SLA Compliance</Text>}
              value={Math.round(metrics.slaCompliance)}
              suffix="%"
              valueStyle={{
                color: metrics.slaCompliance >= 90 ? '#52c41a' : '#f5222d',
                fontSize: '24px',
                fontWeight: 'bold'
              }}
            />
            <Progress
              percent={metrics.slaCompliance}
              showInfo={false}
              strokeColor={metrics.slaCompliance >= 90 ? '#52c41a' : '#f5222d'}
              size="small"
              className="mt-2"
            />
          </Card>
        </Col> */}
      </Row>

      {/* Enhanced Charts Row */}
      <Row gutter={[16, 16]} className="mb-6">
        {/* Case Trends Chart */}
        <Col xs={24} lg={12}>
          <Card
            className="shadow-lg border-0 hover:shadow-xl transition-all duration-300"
            title={
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <BarChart3 size={16} className="text-blue-600" />
                </div>
                <Text strong className="text-gray-800">Case Trends (Last 7 Days)</Text>
              </div>
            }
            extra={
              <Badge count={metrics.caseTrends.reduce((sum, day) => sum + day.total, 0)} color="blue" />
            }
          >
            <ResponsiveContainer width="100%" height={320}>
              <AreaChart data={metrics.caseTrends} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                <defs>
                  <linearGradient id="colorTotal" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#1890ff" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#1890ff" stopOpacity={0.1}/>
                  </linearGradient>
                  <linearGradient id="colorResolved" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#52c41a" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#52c41a" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="date"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#666' }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#666' }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                  }}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="total"
                  stroke="#1890ff"
                  fillOpacity={1}
                  fill="url(#colorTotal)"
                  name="Total Cases"
                  strokeWidth={2}
                />
                <Area
                  type="monotone"
                  dataKey="resolved"
                  stroke="#52c41a"
                  fillOpacity={1}
                  fill="url(#colorResolved)"
                  name="Resolved Cases"
                  strokeWidth={2}
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* Status Distribution */}
        <Col xs={24} lg={12}>
          <Card
            className="shadow-lg border-0 hover:shadow-xl transition-all duration-300"
            title={
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <PieChart size={16} className="text-purple-600" />
                </div>
                <Text strong className="text-gray-800">Cases by Status</Text>
              </div>
            }
            extra={
              <Badge count={Object.keys(metrics.casesByStatus).length} color="purple" />
            }
          >
            <ResponsiveContainer width="100%" height={320}>
              <RechartsPieChart>
                <Pie
                  data={statusChartData}
                  cx="50%"
                  cy="50%"
                  outerRadius={90}
                  innerRadius={40}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  labelLine={false}
                >
                  {statusChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                  }}
                />
                <Legend />
              </RechartsPieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* Enhanced Priority and Category Charts */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} lg={12}>
          <Card
            className="shadow-lg border-0 hover:shadow-xl transition-all duration-300"
            title={
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-red-100 rounded-lg">
                  <Target size={16} className="text-red-600" />
                </div>
                <Text strong className="text-gray-800">Cases by Priority</Text>
              </div>
            }
            extra={
              <Badge count={priorityChartData.length} color="red" />
            }
          >
            <ResponsiveContainer width="100%" height={280}>
              <BarChart data={priorityChartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#666' }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#666' }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                  }}
                />
                <Bar dataKey="value" radius={[4, 4, 0, 0]}>
                  {priorityChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card
            className="shadow-lg border-0 hover:shadow-xl transition-all duration-300"
            title={
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Users size={16} className="text-green-600" />
                </div>
                <Text strong className="text-gray-800">Cases by Category</Text>
              </div>
            }
            extra={
              <Badge count={categoryChartData.length} color="green" />
            }
          >
            <ResponsiveContainer width="100%" height={280}>
              <BarChart data={categoryChartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#666' }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#666' }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                  }}
                />
                <Bar dataKey="value" radius={[4, 4, 0, 0]}>
                  {categoryChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* Enhanced Recent Cases Table */}
      <Row>
        <Col span={24}>
          <Card
            className="shadow-lg border-0 hover:shadow-xl transition-all duration-300"
            title={
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-indigo-100 rounded-lg">
                    <FileText size={16} className="text-indigo-600" />
                  </div>
                  <Text strong className="text-gray-800">Recent Cases</Text>
                </div>
                <Badge count={recentCases.length} color="indigo" />
              </div>
            }
            extra={
              <Space>
                <AntTooltip title="View All Cases">
                  <Button
                    type="link"
                    size="small"
                    onClick={() => window.location.href = '/case'}
                  >
                    View All →
                  </Button>
                </AntTooltip>
              </Space>
            }
          >
            <Table
              columns={recentCasesColumns}
              dataSource={recentCases}
              rowKey="id"
              pagination={false}
              size="middle"
              scroll={{ x: 800 }}
              className="custom-table"
              rowClassName={(record, index) =>
                index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
              }
            />
          </Card>
        </Col>
      </Row>

      {/* Custom Styles */}
      <style jsx>{`
        .custom-table .ant-table-thead > tr > th {
          background: linear-gradient(90deg, #f8fafc 0%, #e2e8f0 100%);
          border-bottom: 2px solid #e2e8f0;
          font-weight: 600;
          color: #374151;
        }
        .table-row-light {
          background-color: #fafafa;
        }
        .table-row-dark {
          background-color: #ffffff;
        }
        .custom-table .ant-table-tbody > tr:hover > td {
          background-color: #e6f7ff !important;
        }
      `}</style>
    </div>
  );
};

export default Dashboard;
